"""
Main Document Generator Script
QS World University Rankings Analysis - Professional Documents Generation
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
"""

import os
import sys
import traceback
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from report_generator.document_generator import ProfessionalDocumentGenerator
from report_generator.word_generator import WordReportGenerator
from report_generator.ppt_generator import PowerPointGenerator

def main():
    """
    Generate comprehensive MS Word report and PowerPoint presentation.
    """
    print("=" * 80)
    print("PROFESSIONAL DOCUMENT GENERATION")
    print("QS World University Rankings Analysis")
    print("Symbiosis International (Deemed University)")
    print("=" * 80)
    
    # Configuration
    base_dir = os.path.dirname(__file__)
    data_dir = os.path.join(base_dir, "output")
    output_dir = os.path.join(base_dir, "final_documents")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        print("\n1. Initializing document generator...")
        
        # Initialize the professional document generator
        doc_generator = ProfessionalDocumentGenerator(data_dir, output_dir)
        
        print(f"   ✓ Data loaded from: {data_dir}")
        print(f"   ✓ Output directory: {output_dir}")
        
        print("\n2. Generating high-resolution charts and visualizations...")
        
        # Generate all charts
        chart_files = doc_generator.generate_all_charts()
        
        print(f"   ✓ Generated {len(chart_files)} professional charts")
        for chart_name in chart_files.keys():
            print(f"     - {chart_name}")
        
        print("\n3. Preparing comprehensive data tables...")
        
        # Create data tables
        tables = doc_generator.create_data_tables()
        
        print(f"   ✓ Created {len(tables)} analysis tables")
        for table_name in tables.keys():
            print(f"     - {table_name}")
        
        print("\n4. Generating MS Word comprehensive report...")
        
        # Generate Word report
        try:
            word_generator = WordReportGenerator(
                doc_generator.analysis_data, 
                chart_files, 
                tables
            )
            
            word_output_path = os.path.join(output_dir, "QS_WUR_Analysis_Symbiosis_Directors_Report.docx")
            word_success = word_generator.generate_word_report(word_output_path)
            
            if word_success:
                print(f"   ✓ Word report generated: {os.path.basename(word_output_path)}")
                print(f"     File size: {os.path.getsize(word_output_path) / (1024*1024):.1f} MB")
            else:
                print("   ⚠ Word report generation failed")
                
        except Exception as e:
            print(f"   ❌ Word generation error: {str(e)}")
        
        print("\n5. Generating PowerPoint presentation...")
        
        # Generate PowerPoint presentation
        try:
            ppt_generator = PowerPointGenerator(
                doc_generator.analysis_data,
                chart_files,
                tables
            )
            
            ppt_output_path = os.path.join(output_dir, "QS_WUR_Analysis_Symbiosis_Directors_Presentation.pptx")
            ppt_success = ppt_generator.generate_presentation(ppt_output_path)
            
            if ppt_success:
                print(f"   ✓ PowerPoint presentation generated: {os.path.basename(ppt_output_path)}")
                print(f"     File size: {os.path.getsize(ppt_output_path) / (1024*1024):.1f} MB")
            else:
                print("   ⚠ PowerPoint presentation generation failed")
                
        except Exception as e:
            print(f"   ❌ PowerPoint generation error: {str(e)}")
        
        print("\n6. Creating executive summary PDF...")
        
        # Create executive summary
        try:
            exec_summary_path = os.path.join(output_dir, "QS_WUR_Executive_Summary_Directors.txt")
            create_executive_summary(doc_generator.analysis_data, exec_summary_path)
            print(f"   ✓ Executive summary created: {os.path.basename(exec_summary_path)}")
        except Exception as e:
            print(f"   ❌ Executive summary error: {str(e)}")
        
        print("\n7. Final deliverables package...")
        
        # List all generated files
        deliverables = []
        for file in os.listdir(output_dir):
            if file.endswith(('.docx', '.pptx', '.txt', '.pdf')):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                deliverables.append((file, file_size))
        
        print(f"   ✓ Generated {len(deliverables)} deliverable files:")
        for filename, size in deliverables:
            print(f"     - {filename} ({size:.1f} MB)")
        
        # Create README for deliverables
        readme_path = os.path.join(output_dir, "README_DELIVERABLES.txt")
        create_deliverables_readme(readme_path, deliverables)
        
        print("\n" + "=" * 80)
        print("DOCUMENT GENERATION COMPLETE")
        print("=" * 80)
        print(f"📁 All files saved to: {output_dir}")
        print(f"📊 Total deliverables: {len(deliverables)}")
        print(f"📈 Charts generated: {len(chart_files)}")
        print(f"📋 Data tables: {len(tables)}")
        print("\n🎯 Ready for directors meeting presentation!")
        
        # Display next steps
        print("\nNEXT STEPS:")
        print("1. Review generated Word report for comprehensive analysis")
        print("2. Practice PowerPoint presentation for directors meeting")
        print("3. Prepare for Q&A based on detailed analysis findings")
        print("4. Consider printing executive summary for distribution")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during document generation: {str(e)}")
        traceback.print_exc()
        return False

def create_executive_summary(analysis_data: dict, output_path: str):
    """Create executive summary text file."""
    
    summary_content = f"""
QS WORLD UNIVERSITY RANKINGS ANALYSIS
EXECUTIVE SUMMARY
Symbiosis International (Deemed University)

Generated: {datetime.now().strftime('%B %d, %Y')}
Prepared by: Dr. Dharmendra Pandey, Deputy Director QMB & Head QA

OVERVIEW
========
Symbiosis International (Deemed University) entered the QS World University Rankings in 2025 
at position 641, marking a significant milestone in global recognition. The university currently 
ranks 696th globally (2026), positioning it 5th among 24 Indian private institutions.

KEY PERFORMANCE HIGHLIGHTS
==========================
• Global Ranking: 696 (2026) | Change: -55 positions from 2025
• Employer Reputation: Exceptional strength - Global Rank 51
• Institutional Focus: Successful transition from "Focused" to "Comprehensive"
• Competitive Position: Top 21% among Indian private institutions
• Research Classification: Very High research intensity

STRATEGIC STRENGTHS
==================
1. EMPLOYER REPUTATION EXCELLENCE
   - Global rank 51 with score of 94.7
   - Places Symbiosis in top 1% worldwide for industry recognition
   - Demonstrates strong alumni networks and placement success

2. COMPREHENSIVE UNIVERSITY STATUS
   - Strategic transition from Focused (FO) to Comprehensive (CO)
   - Enhanced capacity for interdisciplinary research
   - Broader academic portfolio opportunities

3. RESEARCH INFRASTRUCTURE
   - "Very High" research intensity classification
   - Strong foundation for research excellence initiatives

IMPROVEMENT OPPORTUNITIES
========================
1. Research Output & Citations (Priority: HIGH)
   - Current: Score 3.3, Rank 801
   - Target: 50% improvement within 24 months

2. Academic Reputation (Priority: HIGH)
   - Current: Score 9.2, Rank 701
   - Focus on global academic peer recognition

3. International Collaboration (Priority: MEDIUM)
   - Enhance international research networks
   - Develop strategic global partnerships

STRATEGIC RECOMMENDATIONS
=========================
HIGH PRIORITY (1-2 Years):
• Research Excellence Initiative - Comprehensive program for citations improvement
• International Collaboration Expansion - 3-5 major partnerships

MEDIUM PRIORITY (2-3 Years):
• Comprehensive University Advantage - Leverage interdisciplinary opportunities
• Industry Partnership Amplification - Build on employer reputation strength

EXPECTED OUTCOMES
================
Short-term (1-2 years): Enhanced research output, improved international visibility
Long-term (3-5 years): Target top 500 global ranking, recognized research excellence

CONCLUSION
==========
Symbiosis International demonstrates strong competitive positioning with exceptional 
employer reputation providing a significant advantage. The transition to comprehensive 
university status creates strategic opportunities for growth. Focused implementation 
of research excellence and international collaboration initiatives will drive 
sustained ranking improvements.

For detailed analysis and implementation guidance, refer to the comprehensive 
Word report and PowerPoint presentation.

Contact: <EMAIL> | <EMAIL>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(summary_content.strip())

def create_deliverables_readme(readme_path: str, deliverables: list):
    """Create README file for deliverables package."""
    
    readme_content = f"""
QS WORLD UNIVERSITY RANKINGS ANALYSIS
DELIVERABLES PACKAGE
====================================

Generated: {datetime.now().strftime('%B %d, %Y at %I:%M %p')}
Institution: Symbiosis International (Deemed University)
Prepared by: Dr. Dharmendra Pandey, Deputy Director QMB & Head QA

PURPOSE
=======
This package contains comprehensive analysis of Symbiosis International's performance 
in QS World University Rankings, prepared for directors meeting presentation.

DELIVERABLES INCLUDED
====================
"""
    
    for filename, size in deliverables:
        if filename.endswith('.docx'):
            description = "Comprehensive 25-30 page analytical report with charts, tables, and strategic recommendations"
        elif filename.endswith('.pptx'):
            description = "Executive presentation with 20-25 slides ready for directors meeting"
        elif filename.endswith('.txt') and 'executive' in filename.lower():
            description = "Concise executive summary for distribution and quick reference"
        else:
            description = "Supporting documentation"
        
        readme_content += f"""
{filename} ({size:.1f} MB)
- {description}
"""
    
    readme_content += f"""

USAGE INSTRUCTIONS
==================
1. WORD REPORT: Comprehensive analysis document
   - Review all sections before presentation
   - Use for detailed Q&A preparation
   - Reference for implementation planning

2. POWERPOINT PRESENTATION: Directors meeting slides
   - Designed for 45-60 minute presentation
   - Includes speaker notes and key talking points
   - Professional formatting ready for projection

3. EXECUTIVE SUMMARY: Quick reference document
   - Suitable for distribution to board members
   - Key findings and recommendations overview
   - Can be printed for meeting handouts

TECHNICAL NOTES
===============
- All documents use Symbiosis branding and professional formatting
- Charts and visualizations are high-resolution (300 DPI)
- Compatible with Microsoft Office 2016 or later
- PowerPoint optimized for widescreen presentation

NEXT ACTIONS
============
1. Review Word report for comprehensive understanding
2. Practice PowerPoint presentation timing
3. Prepare for potential Q&A sessions
4. Consider implementation timeline discussions

SUPPORT
=======
For questions or clarifications:
Dr. Dharmendra Pandey
Deputy Director - Quality Management & Benchmarking (QMB)
Head - Quality Assurance (QA)
Email: <EMAIL> | <EMAIL>

Generated using advanced analytics and professional document automation.
All data sourced from official QS World University Rankings publications.
"""
    
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 All documents generated successfully!")
    else:
        print("\n❌ Document generation encountered errors. Please check the output above.")