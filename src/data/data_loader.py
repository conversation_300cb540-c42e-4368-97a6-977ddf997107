"""
Module: data_loader
Description: Comprehensive data loading and preprocessing for QS World University Rankings analysis
Author: Dr. <PERSON><PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- pandas
- numpy
- os
- logging
"""

import pandas as pd
import numpy as np
import os
import logging
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSDataLoader:
    """
    Comprehensive data loader for QS World University Rankings datasets.
    
    This class handles loading, preprocessing, and standardization of QS WUR data
    across multiple years with robust error handling and data validation.
    """
    
    def __init__(self, data_directory: str):
        """
        Initialize the QS Data Loader.
        
        Parameters
        ----------
        data_directory : str
            Path to the directory containing QS WUR CSV files
        """
        self.data_directory = data_directory
        self.datasets = {}
        self.combined_data = None
        self.file_mapping = {
            2022: "2022 QS World University Rankings.csv",
            2023: "2023 QS World University Rankings.csv", 
            2024: "2024 QS World University Rankings.csv",
            2025: "2025 QS World University Ranking.csv",
            2026: "2026 QS World University Rankings.csv"
        }
        
    def load_all_datasets(self) -> Dict[int, pd.DataFrame]:
        """
        Load all QS WUR datasets from 2022-2026.
        
        Returns
        -------
        Dict[int, pd.DataFrame]
            Dictionary with year as key and DataFrame as value
        """
        logger.info("Loading all QS World University Rankings datasets...")
        
        for year, filename in self.file_mapping.items():
            filepath = os.path.join(self.data_directory, filename)
            
            if os.path.exists(filepath):
                try:
                    # Try different encodings to handle various CSV formats
                    encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
                    df = None
                    
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(filepath, encoding=encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if df is None:
                        raise ValueError(f"Could not read file with any supported encoding")
                    
                    df = self._standardize_columns(df, year)
                    df = self._clean_data(df, year)
                    df['Year'] = year
                    self.datasets[year] = df
                    logger.info(f"Successfully loaded {year} data: {len(df)} institutions")
                except Exception as e:
                    logger.error(f"Error loading {year} data: {str(e)}")
            else:
                logger.warning(f"File not found: {filepath}")
                
        return self.datasets
    
    def _standardize_columns(self, df: pd.DataFrame, year: int) -> pd.DataFrame:
        """
        Standardize column names across different years.
        
        Parameters
        ----------
        df : pd.DataFrame
            Raw dataframe from CSV
        year : int
            Year of the dataset
            
        Returns
        -------
        pd.DataFrame
            DataFrame with standardized column names
        """
        # Create standardized column mapping
        column_mapping = {
            f'Rank_{year}': 'Rank',
            'Institution_Name': 'Institution',
            'Country': 'Country',
            'Private/Government': 'Type',
            'Size': 'Size',
            'Focus': 'Focus',
            'Research': 'Research_Intensity',
            'Academic_Reputation_Score': 'Academic_Reputation_Score',
            'Academic_Reputation_Rank': 'Academic_Reputation_Rank',
            'Employer_Reputation_Score': 'Employer_Reputation_Score',
            'Employer_Reputation_Rank': 'Employer_Reputation_Rank',
            'Faculty_Student_Score': 'Faculty_Student_Score',
            'Faculty_Student_Rank': 'Faculty_Student_Rank',
            'Citations_per_Faculty_Score': 'Citations_per_Faculty_Score',
            'Citations_per_Faculty_Rank': 'Citations_per_Faculty_Rank',
            'International_Faculty_Score': 'International_Faculty_Score',
            'International_Faculty_Rank': 'International_Faculty_Rank',
            'International_Students_Score': 'International_Students_Score',
            'International_Students_Rank': 'International_Students_Rank',
            'International_Students_Diversity_Score': 'International_Students_Diversity_Score',
            'International_Students_Diversity_Rank': 'International_Students_Diversity_Rank',
            'International_Research_Network_Score': 'International_Research_Network_Score',
            'International_Research_Network_Rank': 'International_Research_Network_Rank',
            'Employment_Outcomes_Score': 'Employment_Outcomes_Score',
            'Employment_Outcomes_Rank': 'Employment_Outcomes_Rank',
            'Sustainability_Score': 'Sustainability_Score',
            'Sustainability_Rank': 'Sustainability_Rank',
            'Overall_Score': 'Overall_Score'
        }
        
        # Rename columns that exist in the dataframe
        existing_columns = {old: new for old, new in column_mapping.items() if old in df.columns}
        df = df.rename(columns=existing_columns)
        
        return df
    
    def _clean_data(self, df: pd.DataFrame, year: int) -> pd.DataFrame:
        """
        Clean and preprocess the data.
        
        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to clean
        year : int
            Year of the dataset
            
        Returns
        -------
        pd.DataFrame
            Cleaned DataFrame
        """
        # Handle missing values consistently
        df = df.replace(['Nan', 'nan', 'NaN', ''], np.nan)
        
        # Convert numeric columns
        numeric_columns = [col for col in df.columns if 'Score' in col or 'Rank' in col or col == 'Rank']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Clean institution names
        if 'Institution' in df.columns:
            df['Institution'] = df['Institution'].str.strip()
        
        # Standardize country names
        if 'Country' in df.columns:
            df['Country'] = df['Country'].str.strip()
            
        # Standardize type classifications
        if 'Type' in df.columns:
            df['Type'] = df['Type'].replace({'Not Sure': np.nan})
            
        return df
    
    def get_combined_dataset(self) -> pd.DataFrame:
        """
        Combine all years into a single dataset.
        
        Returns
        -------
        pd.DataFrame
            Combined dataset with all years
        """
        if self.combined_data is None:
            if not self.datasets:
                self.load_all_datasets()
            
            combined_list = []
            for year, df in self.datasets.items():
                combined_list.append(df)
            
            self.combined_data = pd.concat(combined_list, ignore_index=True, sort=False)
            logger.info(f"Combined dataset created: {len(self.combined_data)} total records")
            
        return self.combined_data
    
    def get_indian_institutions(self) -> pd.DataFrame:
        """
        Filter for Indian institutions only.
        
        Returns
        -------
        pd.DataFrame
            DataFrame containing only Indian institutions
        """
        combined_df = self.get_combined_dataset()
        indian_df = combined_df[combined_df['Country'] == 'India'].copy()
        logger.info(f"Found {len(indian_df)} Indian institution records across all years")
        return indian_df
    
    def get_indian_private_institutions(self) -> pd.DataFrame:
        """
        Filter for Indian private institutions only.
        
        Returns
        -------
        pd.DataFrame
            DataFrame containing only Indian private institutions
        """
        indian_df = self.get_indian_institutions()
        private_df = indian_df[indian_df['Type'] == 'Private'].copy()
        logger.info(f"Found {len(private_df)} Indian private institution records across all years")
        return private_df
    
    def get_symbiosis_data(self) -> pd.DataFrame:
        """
        Extract Symbiosis International (Deemed University) data.
        
        Returns
        -------
        pd.DataFrame
            DataFrame containing only Symbiosis data
        """
        combined_df = self.get_combined_dataset()
        symbiosis_df = combined_df[
            combined_df['Institution'].str.contains('Symbiosis', case=False, na=False)
        ].copy()
        logger.info(f"Found {len(symbiosis_df)} Symbiosis records across all years")
        return symbiosis_df
    
    def get_institutional_focus_analysis(self) -> pd.DataFrame:
        """
        Analyze institutions by their focus classification.
        
        Returns
        -------
        pd.DataFrame
            DataFrame with focus classification analysis
        """
        combined_df = self.get_combined_dataset()
        
        # Group by focus type and calculate performance metrics
        focus_analysis = combined_df.groupby(['Focus', 'Year']).agg({
            'Rank': ['count', 'mean', 'median', 'std'],
            'Overall_Score': ['mean', 'median', 'std'],
            'Academic_Reputation_Score': 'mean',
            'Employer_Reputation_Score': 'mean',
            'Citations_per_Faculty_Score': 'mean'
        }).round(2)
        
        focus_analysis.columns = ['_'.join(col).strip() for col in focus_analysis.columns]
        focus_analysis = focus_analysis.reset_index()
        
        return focus_analysis
    
    def export_processed_data(self, output_directory: str) -> None:
        """
        Export processed datasets to CSV files.
        
        Parameters
        ----------
        output_directory : str
            Directory to save processed datasets
        """
        os.makedirs(output_directory, exist_ok=True)
        
        # Export individual year datasets
        for year, df in self.datasets.items():
            filepath = os.path.join(output_directory, f"qs_wur_{year}_processed.csv")
            df.to_csv(filepath, index=False)
            logger.info(f"Exported {year} processed data to {filepath}")
        
        # Export combined dataset
        combined_df = self.get_combined_dataset()
        combined_filepath = os.path.join(output_directory, "qs_wur_combined_2022_2026.csv")
        combined_df.to_csv(combined_filepath, index=False)
        
        # Export Indian institutions
        indian_df = self.get_indian_institutions()
        indian_filepath = os.path.join(output_directory, "indian_institutions_2022_2026.csv")
        indian_df.to_csv(indian_filepath, index=False)
        
        # Export Indian private institutions
        private_df = self.get_indian_private_institutions()
        private_filepath = os.path.join(output_directory, "indian_private_institutions_2022_2026.csv")
        private_df.to_csv(private_filepath, index=False)
        
        # Export Symbiosis data
        symbiosis_df = self.get_symbiosis_data()
        symbiosis_filepath = os.path.join(output_directory, "symbiosis_data_2022_2026.csv")
        symbiosis_df.to_csv(symbiosis_filepath, index=False)
        
        logger.info("All processed datasets exported successfully")

def load_qs_data(data_directory: str) -> QSDataLoader:
    """
    Convenience function to create and initialize QS Data Loader.
    
    Parameters
    ----------
    data_directory : str
        Path to the directory containing QS WUR CSV files
        
    Returns
    -------
    QSDataLoader
        Initialized data loader with all datasets loaded
    """
    loader = QSDataLoader(data_directory)
    loader.load_all_datasets()
    return loader

if __name__ == "__main__":
    # Example usage
    data_dir = "/Users/<USER>/Downloads/AI Projects Analysis Download folder/Rankings Analysis/QS WUR/QS WUR v1"
    output_dir = "/Users/<USER>/Downloads/AI Projects Analysis Download folder/Rankings Analysis/QS WUR/QS WUR v1/output"
    
    # Load and process data
    loader = load_qs_data(data_dir)
    
    # Export processed datasets
    loader.export_processed_data(output_dir)
    
    # Display summary statistics
    print("\n=== QS WUR Data Loading Summary ===")
    for year, df in loader.datasets.items():
        print(f"{year}: {len(df)} institutions")
    
    print(f"\nIndian Institutions: {len(loader.get_indian_institutions())}")
    print(f"Indian Private Institutions: {len(loader.get_indian_private_institutions())}")
    print(f"Symbiosis Records: {len(loader.get_symbiosis_data())}")