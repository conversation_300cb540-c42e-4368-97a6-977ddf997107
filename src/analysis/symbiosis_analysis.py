"""
Module: symbiosis_analysis
Description: Comprehensive analysis of Symbiosis International (Deemed University) performance
Author: Dr. <PERSON><PERSON><PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- pandas
- numpy
- scipy
- matplotlib
- seaborn
"""

import pandas as pd
import numpy as np
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class SymbiosisAnalyzer:
    """
    Comprehensive analyzer for Symbiosis International (Deemed University) performance
    in QS World University Rankings with comparative analysis capabilities.
    """
    
    def __init__(self, data_loader):
        """
        Initialize the Symbiosis Analyzer.
        
        Parameters
        ----------
        data_loader : QSDataLoader
            Initialized data loader with QS WUR datasets
        """
        self.data_loader = data_loader
        self.symbiosis_data = None
        self.indian_private_data = None
        self.peer_institutions = None
        self.performance_metrics = None
        
    def load_symbiosis_data(self) -> pd.DataFrame:
        """
        Load and prepare Symbiosis-specific data.
        
        Returns
        -------
        pd.DataFrame
            Symbiosis performance data across years
        """
        self.symbiosis_data = self.data_loader.get_symbiosis_data()
        
        if self.symbiosis_data.empty:
            logger.warning("No Symbiosis data found in the datasets")
            return pd.DataFrame()
        
        # Add performance analysis columns
        self.symbiosis_data = self._add_performance_indicators(self.symbiosis_data)
        
        logger.info(f"Loaded Symbiosis data: {len(self.symbiosis_data)} records")
        return self.symbiosis_data
    
    def _add_performance_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add performance indicator columns to the dataset.
        
        Parameters
        ----------
        df : pd.DataFrame
            Base dataframe
            
        Returns
        -------
        pd.DataFrame
            DataFrame with additional performance indicators
        """
        df = df.copy()
        
        # Calculate year-over-year changes
        df = df.sort_values('Year')
        
        # Ranking change
        df['Rank_Change'] = df['Rank'].diff()
        df['Rank_Change_Pct'] = (df['Rank_Change'] / df['Rank'].shift(1)) * 100
        
        # Score changes for key metrics
        score_columns = [col for col in df.columns if 'Score' in col and col != 'Overall_Score']
        for col in score_columns:
            if col in df.columns:
                df[f'{col}_Change'] = df[col].diff()
                df[f'{col}_Change_Pct'] = (df[f'{col}_Change'] / df[col].shift(1)) * 100
        
        # Performance categories
        df['Performance_Category'] = df.apply(self._categorize_performance, axis=1)
        
        return df
    
    def _categorize_performance(self, row) -> str:
        """
        Categorize institutional performance based on ranking.
        
        Parameters
        ----------
        row : pd.Series
            Row from the dataframe
            
        Returns
        -------
        str
            Performance category
        """
        rank = row['Rank']
        if pd.isna(rank):
            return 'Not Ranked'
        elif rank <= 100:
            return 'Top 100'
        elif rank <= 200:
            return 'Top 200'
        elif rank <= 500:
            return 'Top 500'
        elif rank <= 1000:
            return 'Top 1000'
        else:
            return 'Beyond 1000'
    
    def analyze_institutional_focus_impact(self) -> Dict:
        """
        Analyze the impact of institutional focus change from FO to CO.
        
        Returns
        -------
        Dict
            Analysis results of focus change impact
        """
        if self.symbiosis_data is None:
            self.load_symbiosis_data()
        
        focus_analysis = {}
        
        # Get focus changes for Symbiosis
        symbiosis_focus = self.symbiosis_data[['Year', 'Focus', 'Rank', 'Overall_Score']].copy()
        
        # Analyze focus change impact
        fo_data = symbiosis_focus[symbiosis_focus['Focus'] == 'FO']
        co_data = symbiosis_focus[symbiosis_focus['Focus'] == 'CO']
        
        focus_analysis['focus_change_detected'] = len(fo_data) > 0 and len(co_data) > 0
        
        if focus_analysis['focus_change_detected']:
            focus_analysis['fo_period'] = {
                'years': fo_data['Year'].tolist(),
                'avg_rank': fo_data['Rank'].mean(),
                'avg_score': fo_data['Overall_Score'].mean()
            }
            
            focus_analysis['co_period'] = {
                'years': co_data['Year'].tolist(),
                'avg_rank': co_data['Rank'].mean(),
                'avg_score': co_data['Overall_Score'].mean()
            }
            
            # Calculate impact metrics
            rank_change = focus_analysis['co_period']['avg_rank'] - focus_analysis['fo_period']['avg_rank']
            focus_analysis['rank_impact'] = rank_change
            focus_analysis['rank_impact_direction'] = 'Improved' if rank_change < 0 else 'Declined'
            
            score_change = focus_analysis['co_period']['avg_score'] - focus_analysis['fo_period']['avg_score']
            focus_analysis['score_impact'] = score_change
            focus_analysis['score_impact_direction'] = 'Improved' if score_change > 0 else 'Declined'
        
        # Compare with other institutions that made similar changes
        all_indian_private = self.data_loader.get_indian_private_institutions()
        focus_changers = self._identify_focus_changers(all_indian_private)
        focus_analysis['peer_focus_changes'] = focus_changers
        
        return focus_analysis
    
    def _identify_focus_changers(self, df: pd.DataFrame) -> List[Dict]:
        """
        Identify institutions that changed their focus classification.
        
        Parameters
        ----------
        df : pd.DataFrame
            Indian private institutions data
            
        Returns
        -------
        List[Dict]
            List of institutions with focus changes
        """
        focus_changers = []
        
        for institution in df['Institution'].unique():
            inst_data = df[df['Institution'] == institution]
            
            if len(inst_data) > 1:
                unique_focus = inst_data['Focus'].dropna().unique()
                if len(unique_focus) > 1:
                    focus_changers.append({
                        'institution': institution,
                        'focus_changes': unique_focus.tolist(),
                        'years_present': inst_data['Year'].tolist(),
                        'rank_range': [inst_data['Rank'].min(), inst_data['Rank'].max()]
                    })
        
        return focus_changers
    
    def comparative_analysis_with_peers(self) -> Dict:
        """
        Perform comprehensive comparative analysis with peer institutions.
        
        Returns
        -------
        Dict
            Comparative analysis results
        """
        if self.indian_private_data is None:
            self.indian_private_data = self.data_loader.get_indian_private_institutions()
        
        # Define peer institutions (similar rank range and characteristics)
        symbiosis_rank_range = self.symbiosis_data['Rank'].min(), self.symbiosis_data['Rank'].max()
        
        # Identify peer institutions
        peer_candidates = self.indian_private_data[
            (self.indian_private_data['Rank'] >= symbiosis_rank_range[0] - 200) &
            (self.indian_private_data['Rank'] <= symbiosis_rank_range[1] + 200) &
            (self.indian_private_data['Institution'] != 'Symbiosis International (Deemed University)')
        ]
        
        # Select top peers based on consistency and performance
        peer_performance = peer_candidates.groupby('Institution').agg({
            'Rank': ['mean', 'count', 'std'],
            'Overall_Score': 'mean',
            'Employer_Reputation_Score': 'mean'
        }).round(2)
        
        peer_performance.columns = ['_'.join(col).strip() for col in peer_performance.columns]
        peer_performance = peer_performance.reset_index()
        
        # Filter for institutions with multiple year data
        consistent_peers = peer_performance[peer_performance['Rank_count'] >= 2]
        
        # Select top 10 most comparable peers
        self.peer_institutions = consistent_peers.nsmallest(10, 'Rank_mean')
        
        # Comparative metrics
        comparative_analysis = {
            'symbiosis_vs_peers': self._calculate_peer_comparison(),
            'ranking_position': self._calculate_ranking_position(),
            'strength_analysis': self._analyze_strengths_vs_peers(),
            'improvement_opportunities': self._identify_improvement_opportunities()
        }
        
        return comparative_analysis
    
    def _calculate_peer_comparison(self) -> Dict:
        """Calculate detailed comparison with peer institutions."""
        if self.symbiosis_data is None:
            return {}
        
        # Get latest Symbiosis data
        latest_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == self.symbiosis_data['Year'].max()]
        
        # Get peer data for comparison
        peer_institutions_list = self.peer_institutions['Institution'].tolist()
        peer_data = self.indian_private_data[
            self.indian_private_data['Institution'].isin(peer_institutions_list)
        ]
        
        # Calculate comparative metrics
        comparison_metrics = {}
        
        # Ranking comparison
        symbiosis_rank = latest_symbiosis['Rank'].iloc[0]
        peer_ranks = peer_data['Rank'].dropna()
        
        comparison_metrics['rank_percentile'] = (peer_ranks < symbiosis_rank).sum() / len(peer_ranks) * 100
        comparison_metrics['better_than_peers'] = (peer_ranks > symbiosis_rank).sum()
        comparison_metrics['worse_than_peers'] = (peer_ranks < symbiosis_rank).sum()
        
        # Score comparisons
        score_columns = ['Academic_Reputation_Score', 'Employer_Reputation_Score', 
                        'Citations_per_Faculty_Score', 'International_Faculty_Score']
        
        for col in score_columns:
            if col in latest_symbiosis.columns:
                symbiosis_score = latest_symbiosis[col].iloc[0]
                peer_scores = peer_data[col].dropna()
                
                if not peer_scores.empty and not pd.isna(symbiosis_score):
                    comparison_metrics[f'{col}_percentile'] = (peer_scores < symbiosis_score).sum() / len(peer_scores) * 100
                    comparison_metrics[f'{col}_vs_peer_avg'] = symbiosis_score - peer_scores.mean()
        
        return comparison_metrics
    
    def _calculate_ranking_position(self) -> Dict:
        """Calculate Symbiosis ranking position among Indian private institutions."""
        latest_year = self.indian_private_data['Year'].max()
        latest_private_data = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        # Sort by rank
        ranked_institutions = latest_private_data.sort_values('Rank')
        
        # Find Symbiosis position
        symbiosis_position = ranked_institutions[
            ranked_institutions['Institution'].str.contains('Symbiosis', case=False, na=False)
        ].index
        
        if len(symbiosis_position) > 0:
            position = (ranked_institutions.index == symbiosis_position[0]).sum()
            total_institutions = len(ranked_institutions)
            
            return {
                'position_among_private': position,
                'total_private_institutions': total_institutions,
                'top_percentile': (position / total_institutions) * 100,
                'institutions_behind': total_institutions - position
            }
        
        return {}
    
    def _analyze_strengths_vs_peers(self) -> Dict:
        """Analyze Symbiosis strengths compared to peer institutions."""
        if self.symbiosis_data is None:
            return {}
        
        latest_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == self.symbiosis_data['Year'].max()]
        
        # Score columns for analysis
        score_columns = [
            'Academic_Reputation_Score', 'Employer_Reputation_Score',
            'Faculty_Student_Score', 'Citations_per_Faculty_Score',
            'International_Faculty_Score', 'International_Students_Score',
            'Employment_Outcomes_Score', 'Sustainability_Score'
        ]
        
        strengths = {}
        
        for col in score_columns:
            if col in latest_symbiosis.columns:
                symbiosis_score = latest_symbiosis[col].iloc[0]
                
                if not pd.isna(symbiosis_score):
                    # Compare with Indian private institutions average
                    peer_avg = self.indian_private_data[col].mean()
                    
                    if not pd.isna(peer_avg):
                        strength_score = symbiosis_score - peer_avg
                        strengths[col] = {
                            'symbiosis_score': symbiosis_score,
                            'peer_average': peer_avg,
                            'difference': strength_score,
                            'is_strength': strength_score > 0
                        }
        
        return strengths
    
    def _identify_improvement_opportunities(self) -> Dict:
        """Identify areas for improvement based on peer comparison."""
        strengths = self._analyze_strengths_vs_peers()
        
        # Identify weaknesses (areas below peer average)
        weaknesses = {k: v for k, v in strengths.items() if not v['is_strength']}
        
        # Sort by improvement potential
        improvement_opportunities = sorted(
            weaknesses.items(),
            key=lambda x: x[1]['difference']  # Most negative difference = highest priority
        )
        
        return {
            'priority_areas': improvement_opportunities[:5],  # Top 5 priority areas
            'total_improvement_areas': len(weaknesses),
            'strength_areas': len([v for v in strengths.values() if v['is_strength']])
        }
    
    def generate_performance_summary(self) -> Dict:
        """
        Generate comprehensive performance summary for Symbiosis.
        
        Returns
        -------
        Dict
            Complete performance summary
        """
        if self.symbiosis_data is None:
            self.load_symbiosis_data()
        
        summary = {
            'institutional_profile': self._get_institutional_profile(),
            'performance_trajectory': self._get_performance_trajectory(),
            'focus_change_impact': self.analyze_institutional_focus_impact(),
            'competitive_position': self.comparative_analysis_with_peers(),
            'key_insights': self._generate_key_insights(),
            'strategic_recommendations': self._generate_recommendations()
        }
        
        return summary
    
    def _get_institutional_profile(self) -> Dict:
        """Get basic institutional profile information."""
        if self.symbiosis_data.empty:
            return {}
        
        latest_data = self.symbiosis_data[self.symbiosis_data['Year'] == self.symbiosis_data['Year'].max()].iloc[0]
        
        return {
            'institution_name': latest_data['Institution'],
            'country': latest_data['Country'],
            'type': latest_data['Type'],
            'size': latest_data['Size'],
            'current_focus': latest_data['Focus'],
            'research_intensity': latest_data['Research_Intensity'],
            'years_in_rankings': len(self.symbiosis_data),
            'ranking_years': sorted(self.symbiosis_data['Year'].tolist())
        }
    
    def _get_performance_trajectory(self) -> Dict:
        """Calculate performance trajectory metrics."""
        trajectory = {
            'ranking_trend': [],
            'score_trends': {},
            'year_over_year_changes': []
        }
        
        for _, row in self.symbiosis_data.iterrows():
            trajectory['ranking_trend'].append({
                'year': row['Year'],
                'rank': row['Rank'],
                'overall_score': row['Overall_Score']
            })
        
        # Calculate trends for key metrics
        score_columns = ['Academic_Reputation_Score', 'Employer_Reputation_Score', 
                        'Citations_per_Faculty_Score', 'International_Faculty_Score']
        
        for col in score_columns:
            if col in self.symbiosis_data.columns:
                trajectory['score_trends'][col] = self.symbiosis_data[['Year', col]].to_dict('records')
        
        return trajectory
    
    def _generate_key_insights(self) -> List[str]:
        """Generate key insights from the analysis."""
        insights = []
        
        if self.symbiosis_data is None or self.symbiosis_data.empty:
            return ["Insufficient data available for analysis"]
        
        # Ranking insights
        ranks = self.symbiosis_data['Rank'].tolist()
        if len(ranks) > 1:
            if ranks[-1] > ranks[0]:
                insights.append(f"Ranking declined from {ranks[0]} to {ranks[-1]} ({ranks[-1] - ranks[0]} positions)")
            else:
                insights.append(f"Ranking improved from {ranks[0]} to {ranks[-1]} ({ranks[0] - ranks[-1]} positions)")
        
        # Employer reputation strength
        employer_scores = self.symbiosis_data['Employer_Reputation_Score'].dropna()
        if not employer_scores.empty:
            avg_employer_score = employer_scores.mean()
            if avg_employer_score > 90:
                insights.append(f"Exceptional employer reputation with average score of {avg_employer_score:.1f}")
            elif avg_employer_score > 80:
                insights.append(f"Strong employer reputation with average score of {avg_employer_score:.1f}")
        
        # Focus change impact
        if len(self.symbiosis_data['Focus'].dropna().unique()) > 1:
            insights.append("Institution underwent focus classification change from 'Focused' to 'Comprehensive'")
        
        # Areas needing attention
        citation_scores = self.symbiosis_data['Citations_per_Faculty_Score'].dropna()
        if not citation_scores.empty and citation_scores.mean() < 10:
            insights.append("Citations per faculty requires significant improvement for research impact")
        
        return insights
    
    def _generate_recommendations(self) -> List[str]:
        """Generate strategic recommendations based on analysis."""
        recommendations = []
        
        # Based on strengths and weaknesses
        strengths = self._analyze_strengths_vs_peers()
        
        # Leverage employer reputation
        if 'Employer_Reputation_Score' in strengths and strengths['Employer_Reputation_Score']['is_strength']:
            recommendations.append("Leverage strong employer reputation for industry partnerships and applied research collaborations")
        
        # Improve research output
        if 'Citations_per_Faculty_Score' in strengths and not strengths['Citations_per_Faculty_Score']['is_strength']:
            recommendations.append("Implement research excellence initiatives to improve citations per faculty")
        
        # International expansion
        if 'International_Faculty_Score' in strengths and not strengths['International_Faculty_Score']['is_strength']:
            recommendations.append("Develop international faculty recruitment strategy to enhance global presence")
        
        # Comprehensive advantage
        focus_analysis = self.analyze_institutional_focus_impact()
        if focus_analysis.get('focus_change_detected'):
            recommendations.append("Capitalize on comprehensive university status by strengthening interdisciplinary research and programs")
        
        return recommendations