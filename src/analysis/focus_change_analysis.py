"""
Module: focus_change_analysis
Description: Analysis of institutional focus classification changes and their impact on performance
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- pandas
- numpy
- scipy
- matplotlib
- seaborn
"""

import pandas as pd
import numpy as np
from scipy import stats
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class FocusChangeAnalyzer:
    """
    Analyzer for institutional focus classification changes and their impact
    on QS World University Rankings performance.
    """
    
    def __init__(self, data_loader):
        """
        Initialize the Focus Change Analyzer.
        
        Parameters
        ----------
        data_loader : QSDataLoader
            Initialized data loader with QS WUR datasets
        """
        self.data_loader = data_loader
        self.combined_data = None
        self.focus_definitions = {
            'FC': 'Full Comprehensive',
            'CO': 'Comprehensive', 
            'FO': 'Focused',
            'SP': 'Specialist'
        }
        
    def load_data(self) -> pd.DataFrame:
        """
        Load combined dataset for focus change analysis.
        
        Returns
        -------
        pd.DataFrame
            Combined dataset with all years
        """
        self.combined_data = self.data_loader.get_combined_dataset()
        logger.info(f"Loaded combined data: {len(self.combined_data)} records")
        return self.combined_data
    
    def identify_focus_changes(self) -> Dict:
        """
        Identify all institutions that changed their focus classification.
        
        Returns
        -------
        Dict
            Analysis of focus changes across all institutions
        """
        if self.combined_data is None:
            self.load_data()
        
        focus_changes = {}
        
        # Analyze focus changes by institution
        for institution in self.combined_data['Institution'].unique():
            if pd.isna(institution):
                continue  # Skip NaN institution names
            inst_data = self.combined_data[self.combined_data['Institution'] == institution]
            
            if len(inst_data) > 1:  # Need multiple years to detect changes
                focus_values = inst_data['Focus'].dropna().unique()
                
                if len(focus_values) > 1:  # Focus change detected
                    inst_data_sorted = inst_data.sort_values('Year')
                    
                    # Track year-by-year focus changes
                    focus_timeline = []
                    for _, row in inst_data_sorted.iterrows():
                        if not pd.isna(row['Focus']):
                            focus_timeline.append({
                                'year': int(row['Year']),
                                'focus': row['Focus'],
                                'focus_description': self.focus_definitions.get(row['Focus'], row['Focus']),
                                'rank': row['Rank'],
                                'overall_score': row['Overall_Score']
                            })
                    
                    # Analyze impact of focus changes
                    impact_analysis = self._analyze_focus_change_impact(focus_timeline)
                    
                    focus_changes[institution] = {
                        'country': inst_data['Country'].iloc[0],
                        'type': inst_data['Type'].iloc[0] if 'Type' in inst_data.columns else 'Unknown',
                        'focus_changes_detected': focus_values.tolist(),
                        'focus_timeline': focus_timeline,
                        'impact_analysis': impact_analysis,
                        'years_tracked': len(inst_data)
                    }
        
        # Summary statistics
        summary = self._generate_focus_change_summary(focus_changes)
        
        return {
            'institutions_with_changes': focus_changes,
            'summary_statistics': summary,
            'focus_definitions': self.focus_definitions
        }
    
    def _analyze_focus_change_impact(self, timeline: List[Dict]) -> Dict:
        """
        Analyze the impact of focus changes on institutional performance.
        
        Parameters
        ----------
        timeline : List[Dict]
            Timeline of focus changes with performance data
            
        Returns
        -------
        Dict
            Impact analysis results
        """
        if len(timeline) < 2:
            return {'error': 'Insufficient data for impact analysis'}
        
        impact_analysis = {}
        
        # Group by focus periods
        focus_periods = {}
        current_focus = None
        current_period = []
        
        for entry in timeline:
            if entry['focus'] != current_focus:
                if current_period:
                    if current_focus not in focus_periods:
                        focus_periods[current_focus] = []
                    focus_periods[current_focus].extend(current_period)
                
                current_focus = entry['focus']
                current_period = [entry]
            else:
                current_period.append(entry)
        
        # Add final period
        if current_period and current_focus:
            if current_focus not in focus_periods:
                focus_periods[current_focus] = []
            focus_periods[current_focus].extend(current_period)
        
        # Calculate performance metrics for each focus period
        for focus, periods in focus_periods.items():
            ranks = [p['rank'] for p in periods if not pd.isna(p['rank'])]
            scores = [p['overall_score'] for p in periods if not pd.isna(p['overall_score'])]
            
            if ranks:
                impact_analysis[focus] = {
                    'focus_description': self.focus_definitions.get(focus, focus),
                    'years_with_focus': [p['year'] for p in periods],
                    'avg_rank': round(np.mean(ranks), 1),
                    'best_rank': int(min(ranks)),
                    'worst_rank': int(max(ranks)),
                    'rank_stability': round(np.std(ranks), 1) if len(ranks) > 1 else 0,
                    'avg_score': round(np.mean(scores), 2) if scores else None,
                    'data_points': len(periods)
                }
        
        # Calculate transition impacts
        if len(focus_periods) > 1:
            focus_sequence = list(focus_periods.keys())
            impact_analysis['transitions'] = []
            
            for i in range(len(focus_sequence) - 1):
                from_focus = focus_sequence[i]
                to_focus = focus_sequence[i + 1]
                
                from_performance = impact_analysis[from_focus]
                to_performance = impact_analysis[to_focus]
                
                rank_change = to_performance['avg_rank'] - from_performance['avg_rank']
                
                transition_impact = {
                    'from_focus': from_focus,
                    'to_focus': to_focus,
                    'from_description': self.focus_definitions.get(from_focus, from_focus),
                    'to_description': self.focus_definitions.get(to_focus, to_focus),
                    'rank_change': round(rank_change, 1),
                    'rank_change_direction': 'Improved' if rank_change < 0 else 'Declined',
                    'impact_magnitude': abs(rank_change)
                }
                
                if from_performance['avg_score'] and to_performance['avg_score']:
                    score_change = to_performance['avg_score'] - from_performance['avg_score']
                    transition_impact['score_change'] = round(score_change, 2)
                    transition_impact['score_change_direction'] = 'Improved' if score_change > 0 else 'Declined'
                
                impact_analysis['transitions'].append(transition_impact)
        
        return impact_analysis
    
    def _generate_focus_change_summary(self, focus_changes: Dict) -> Dict:
        """
        Generate summary statistics for focus changes.
        
        Parameters
        ----------
        focus_changes : Dict
            Dictionary of institutions with focus changes
            
        Returns
        -------
        Dict
            Summary statistics
        """
        total_institutions = len(focus_changes)
        
        # Count transitions by type
        transition_counts = {}
        countries = {}
        institution_types = {}
        
        for inst, data in focus_changes.items():
            # Country distribution
            country = data['country']
            countries[country] = countries.get(country, 0) + 1
            
            # Institution type distribution
            inst_type = data['type']
            institution_types[inst_type] = institution_types.get(inst_type, 0) + 1
            
            # Transition types
            if 'transitions' in data['impact_analysis']:
                for transition in data['impact_analysis']['transitions']:
                    transition_key = f"{transition['from_focus']} → {transition['to_focus']}"
                    transition_counts[transition_key] = transition_counts.get(transition_key, 0) + 1
        
        # Most common transitions
        most_common_transitions = sorted(transition_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_institutions_with_changes': total_institutions,
            'country_distribution': countries,
            'institution_type_distribution': institution_types,
            'transition_type_counts': transition_counts,
            'most_common_transitions': most_common_transitions[:5],
            'unique_transition_types': len(transition_counts)
        }
    
    def analyze_symbiosis_focus_change(self) -> Dict:
        """
        Specific analysis of Symbiosis International focus change.
        
        Returns
        -------
        Dict
            Detailed analysis of Symbiosis focus change impact
        """
        symbiosis_data = self.data_loader.get_symbiosis_data()
        
        if symbiosis_data.empty:
            return {'error': 'No Symbiosis data found'}
        
        # Check for focus changes in Symbiosis data
        focus_values = symbiosis_data['Focus'].dropna().unique()
        
        if len(focus_values) <= 1:
            return {'no_change_detected': True, 'current_focus': focus_values[0] if len(focus_values) > 0 else 'Unknown'}
        
        # Detailed analysis of Symbiosis focus change
        symbiosis_sorted = symbiosis_data.sort_values('Year')
        
        # Create timeline
        timeline = []
        for _, row in symbiosis_sorted.iterrows():
            timeline.append({
                'year': int(row['Year']),
                'focus': row['Focus'],
                'focus_description': self.focus_definitions.get(row['Focus'], row['Focus']),
                'rank': row['Rank'],
                'overall_score': row['Overall_Score'],
                'academic_reputation': row['Academic_Reputation_Score'] if 'Academic_Reputation_Score' in row else None,
                'employer_reputation': row['Employer_Reputation_Score'] if 'Employer_Reputation_Score' in row else None,
                'citations_per_faculty': row['Citations_per_Faculty_Score'] if 'Citations_per_Faculty_Score' in row else None
            })
        
        # Analyze impact
        impact_analysis = self._analyze_focus_change_impact(timeline)
        
        # Compare with peer institutions that made similar changes
        peer_comparison = self._compare_with_peer_focus_changes(symbiosis_data, focus_values)
        
        # Detailed metric analysis
        metric_analysis = self._analyze_metric_changes_during_focus_transition(timeline)
        
        return {
            'institution': 'Symbiosis International (Deemed University)',
            'focus_change_detected': True,
            'focus_changes': focus_values.tolist(),
            'timeline': timeline,
            'impact_analysis': impact_analysis,
            'peer_comparison': peer_comparison,
            'detailed_metrics': metric_analysis,
            'strategic_implications': self._generate_symbiosis_strategic_implications(impact_analysis, metric_analysis)
        }
    
    def _compare_with_peer_focus_changes(self, symbiosis_data: pd.DataFrame, symbiosis_focus_changes: np.ndarray) -> Dict:
        """
        Compare Symbiosis focus change with similar institutions.
        
        Parameters
        ----------
        symbiosis_data : pd.DataFrame
            Symbiosis performance data
        symbiosis_focus_changes : np.ndarray
            Focus changes detected for Symbiosis
            
        Returns
        -------
        Dict
            Peer comparison analysis
        """
        if self.combined_data is None:
            self.load_data()
        
        # Find institutions with similar focus changes
        similar_changes = []
        
        # Define the focus change pattern for Symbiosis (FO → CO based on earlier analysis)
        symbiosis_pattern = set(symbiosis_focus_changes)
        
        for institution in self.combined_data['Institution'].unique():
            if pd.isna(institution) or 'Symbiosis' in str(institution):
                continue  # Skip Symbiosis itself and NaN values
            
            inst_data = self.combined_data[self.combined_data['Institution'] == institution]
            inst_focus_changes = inst_data['Focus'].dropna().unique()
            
            if len(inst_focus_changes) > 1:
                inst_pattern = set(inst_focus_changes)
                
                # Check for similar patterns
                if symbiosis_pattern == inst_pattern or len(symbiosis_pattern.intersection(inst_pattern)) >= 1:
                    inst_timeline = []
                    for _, row in inst_data.sort_values('Year').iterrows():
                        if not pd.isna(row['Focus']):
                            inst_timeline.append({
                                'year': int(row['Year']),
                                'focus': row['Focus'],
                                'rank': row['Rank']
                            })
                    
                    similar_changes.append({
                        'institution': institution,
                        'country': inst_data['Country'].iloc[0],
                        'focus_pattern': inst_focus_changes.tolist(),
                        'timeline': inst_timeline,
                        'current_rank': inst_data[inst_data['Year'] == inst_data['Year'].max()]['Rank'].iloc[0]
                    })
        
        # Analyze patterns
        peer_analysis = {
            'similar_institutions_found': len(similar_changes),
            'peer_institutions': similar_changes,
            'comparative_performance': self._analyze_peer_focus_change_performance(similar_changes, symbiosis_data)
        }
        
        return peer_analysis
    
    def _analyze_peer_focus_change_performance(self, peers: List[Dict], symbiosis_data: pd.DataFrame) -> Dict:
        """
        Analyze performance of peer institutions with similar focus changes.
        
        Parameters
        ----------
        peers : List[Dict]
            List of peer institutions with similar focus changes
        symbiosis_data : pd.DataFrame
            Symbiosis performance data
            
        Returns
        -------
        Dict
            Comparative performance analysis
        """
        if not peers:
            return {'no_peers_found': True}
        
        # Get Symbiosis current rank
        symbiosis_current_rank = symbiosis_data[symbiosis_data['Year'] == symbiosis_data['Year'].max()]['Rank'].iloc[0]
        
        # Compare performance
        peer_ranks = [peer['current_rank'] for peer in peers if not pd.isna(peer['current_rank'])]
        
        if not peer_ranks:
            return {'insufficient_peer_data': True}
        
        comparison = {
            'symbiosis_rank': int(symbiosis_current_rank),
            'peer_ranks': peer_ranks,
            'peer_avg_rank': round(np.mean(peer_ranks), 1),
            'peer_best_rank': int(min(peer_ranks)),
            'peer_worst_rank': int(max(peer_ranks)),
            'symbiosis_vs_peer_avg': round(symbiosis_current_rank - np.mean(peer_ranks), 1),
            'symbiosis_percentile': round((sum(1 for rank in peer_ranks if rank > symbiosis_current_rank) / len(peer_ranks)) * 100, 1),
            'performance_assessment': 'Above Average' if symbiosis_current_rank < np.mean(peer_ranks) else 'Below Average'
        }
        
        return comparison
    
    def _analyze_metric_changes_during_focus_transition(self, timeline: List[Dict]) -> Dict:
        """
        Analyze specific metric changes during focus transition.
        
        Parameters
        ----------
        timeline : List[Dict]
            Timeline with detailed metrics
            
        Returns
        -------
        Dict
            Detailed metric analysis
        """
        if len(timeline) < 2:
            return {'insufficient_data': True}
        
        # Identify transition points
        focus_transitions = []
        for i in range(1, len(timeline)):
            if timeline[i]['focus'] != timeline[i-1]['focus']:
                focus_transitions.append({
                    'from_year': timeline[i-1]['year'],
                    'to_year': timeline[i]['year'],
                    'from_focus': timeline[i-1]['focus'],
                    'to_focus': timeline[i]['focus'],
                    'before_metrics': timeline[i-1],
                    'after_metrics': timeline[i]
                })
        
        if not focus_transitions:
            return {'no_transitions_found': True}
        
        # Analyze metric changes for each transition
        transition_analysis = []
        
        for transition in focus_transitions:
            before = transition['before_metrics']
            after = transition['after_metrics']
            
            metric_changes = {}
            
            # Analyze key metrics
            metrics_to_analyze = ['rank', 'overall_score', 'academic_reputation', 'employer_reputation', 'citations_per_faculty']
            
            for metric in metrics_to_analyze:
                if before.get(metric) is not None and after.get(metric) is not None:
                    before_val = before[metric]
                    after_val = after[metric]
                    
                    if metric == 'rank':
                        # For ranking, lower is better
                        change = before_val - after_val  # Positive = improvement
                        change_direction = 'Improved' if change > 0 else 'Declined' if change < 0 else 'No Change'
                    else:
                        # For scores, higher is better
                        change = after_val - before_val  # Positive = improvement
                        change_direction = 'Improved' if change > 0 else 'Declined' if change < 0 else 'No Change'
                    
                    metric_changes[metric] = {
                        'before': before_val,
                        'after': after_val,
                        'change': round(change, 2),
                        'change_direction': change_direction,
                        'change_percentage': round((abs(change) / abs(before_val)) * 100, 1) if before_val != 0 else 0
                    }
            
            transition_analysis.append({
                'transition': f"{transition['from_focus']} → {transition['to_focus']}",
                'year_span': f"{transition['from_year']} → {transition['to_year']}",
                'metric_changes': metric_changes
            })
        
        return {
            'transitions_analyzed': len(transition_analysis),
            'transition_details': transition_analysis,
            'overall_impact_summary': self._summarize_overall_impact(transition_analysis)
        }
    
    def _summarize_overall_impact(self, transition_analysis: List[Dict]) -> Dict:
        """
        Summarize overall impact of focus changes.
        
        Parameters
        ----------
        transition_analysis : List[Dict]
            Detailed transition analysis
            
        Returns
        -------
        Dict
            Overall impact summary
        """
        if not transition_analysis:
            return {}
        
        # Aggregate impacts across all transitions
        metric_impacts = {}
        
        for transition in transition_analysis:
            for metric, change_data in transition['metric_changes'].items():
                if metric not in metric_impacts:
                    metric_impacts[metric] = []
                
                metric_impacts[metric].append({
                    'change': change_data['change'],
                    'direction': change_data['change_direction']
                })
        
        # Summarize impacts
        summary = {}
        for metric, impacts in metric_impacts.items():
            improvements = sum(1 for impact in impacts if impact['direction'] == 'Improved')
            declines = sum(1 for impact in impacts if impact['direction'] == 'Declined')
            
            avg_change = np.mean([impact['change'] for impact in impacts])
            
            summary[metric] = {
                'total_transitions': len(impacts),
                'improvements': improvements,
                'declines': declines,
                'net_improvement_rate': round((improvements / len(impacts)) * 100, 1),
                'average_change': round(avg_change, 2),
                'overall_trend': 'Positive' if improvements > declines else 'Negative' if declines > improvements else 'Neutral'
            }
        
        return summary
    
    def _generate_symbiosis_strategic_implications(self, impact_analysis: Dict, metric_analysis: Dict) -> List[str]:
        """
        Generate strategic implications specific to Symbiosis focus change.
        
        Parameters
        ----------
        impact_analysis : Dict
            Impact analysis results
        metric_analysis : Dict
            Detailed metric analysis
            
        Returns
        -------
        List[str]
            Strategic implications and recommendations
        """
        implications = []
        
        # Analyze transitions if available
        if 'transitions' in impact_analysis and impact_analysis['transitions']:
            for transition in impact_analysis['transitions']:
                if transition['from_focus'] == 'FO' and transition['to_focus'] == 'CO':
                    implications.append("Transition from 'Focused' to 'Comprehensive' aligns with institutional growth strategy")
                    
                    if transition['rank_change_direction'] == 'Declined':
                        implications.append("Initial ranking impact may be negative due to expanded evaluation criteria for comprehensive universities")
                    else:
                        implications.append("Successful adaptation to comprehensive university evaluation framework")
        
        # Analyze metric trends
        if 'overall_impact_summary' in metric_analysis:
            summary = metric_analysis['overall_impact_summary']
            
            if 'employer_reputation' in summary and summary['employer_reputation']['overall_trend'] == 'Positive':
                implications.append("Strong employer reputation provides foundation for comprehensive university positioning")
            
            if 'citations_per_faculty' in summary and summary['citations_per_faculty']['overall_trend'] == 'Negative':
                implications.append("Research output enhancement critical for comprehensive university success")
            
            if 'academic_reputation' in summary:
                if summary['academic_reputation']['overall_trend'] == 'Negative':
                    implications.append("Academic reputation building requires focused attention in comprehensive framework")
                else:
                    implications.append("Academic reputation aligns well with comprehensive university positioning")
        
        # Strategic recommendations
        implications.extend([
            "Leverage comprehensive status to develop interdisciplinary programs and research initiatives",
            "Strengthen research infrastructure to support expanded academic portfolio",
            "Maintain strong industry connections while building academic research excellence",
            "Focus on international collaboration opportunities available to comprehensive universities"
        ])
        
        return implications
    
    def analyze_global_focus_trends(self) -> Dict:
        """
        Analyze global trends in institutional focus classifications.
        
        Returns
        -------
        Dict
            Global focus trends analysis
        """
        if self.combined_data is None:
            self.load_data()
        
        # Analyze focus distribution by year
        focus_trends = self.combined_data.groupby(['Year', 'Focus']).size().unstack(fill_value=0)
        
        # Calculate year-over-year changes
        focus_growth_rates = focus_trends.pct_change().fillna(0) * 100
        
        # Overall focus distribution
        total_focus_dist = self.combined_data['Focus'].value_counts()
        
        # Performance by focus type
        focus_performance = self.combined_data.groupby('Focus').agg({
            'Rank': ['mean', 'median', 'std'],
            'Overall_Score': 'mean'
        }).round(2)
        
        focus_performance.columns = ['_'.join(col).strip() for col in focus_performance.columns]
        focus_performance = focus_performance.reset_index()
        
        return {
            'yearly_focus_distribution': focus_trends.to_dict(),
            'focus_growth_rates': focus_growth_rates.to_dict(),
            'total_focus_distribution': total_focus_dist.to_dict(),
            'performance_by_focus': focus_performance.to_dict('records'),
            'focus_definitions': self.focus_definitions,
            'key_insights': self._generate_focus_trend_insights(focus_trends, focus_performance)
        }
    
    def _generate_focus_trend_insights(self, trends: pd.DataFrame, performance: pd.DataFrame) -> List[str]:
        """
        Generate insights from focus trend analysis.
        
        Parameters
        ----------
        trends : pd.DataFrame
            Yearly focus distribution trends
        performance : pd.DataFrame
            Performance by focus type
            
        Returns
        -------
        List[str]
            Key insights about focus trends
        """
        insights = []
        
        # Growth trends
        if 'CO' in trends.columns:
            co_growth = trends['CO'].iloc[-1] - trends['CO'].iloc[0]
            if co_growth > 0:
                insights.append(f"Comprehensive (CO) universities increased by {co_growth} institutions over the analysis period")
        
        if 'FO' in trends.columns:
            fo_growth = trends['FO'].iloc[-1] - trends['FO'].iloc[0]
            if fo_growth < 0:
                insights.append(f"Focused (FO) universities decreased by {abs(fo_growth)} institutions, indicating sector consolidation")
        
        # Performance insights
        performance_sorted = performance.sort_values('Rank_mean')
        if not performance_sorted.empty:
            best_focus = performance_sorted.iloc[0]['Focus']
            insights.append(f"'{best_focus}' institutions show the best average ranking performance")
        
        return insights
    
    def export_focus_analysis(self, output_directory: str) -> None:
        """
        Export focus change analysis results.
        
        Parameters
        ----------
        output_directory : str
            Directory to save analysis results
        """
        import os
        import json
        
        os.makedirs(output_directory, exist_ok=True)
        
        # General focus change analysis
        focus_changes = self.identify_focus_changes()
        with open(os.path.join(output_directory, 'focus_change_analysis.json'), 'w') as f:
            json.dump(focus_changes, f, indent=2)
        
        # Symbiosis specific analysis
        symbiosis_analysis = self.analyze_symbiosis_focus_change()
        with open(os.path.join(output_directory, 'symbiosis_focus_analysis.json'), 'w') as f:
            json.dump(symbiosis_analysis, f, indent=2)
        
        # Global focus trends
        global_trends = self.analyze_global_focus_trends()
        with open(os.path.join(output_directory, 'global_focus_trends.json'), 'w') as f:
            json.dump(global_trends, f, indent=2)
        
        logger.info(f"Focus change analysis results exported to {output_directory}")
        
        return {
            'files_created': [
                'focus_change_analysis.json',
                'symbiosis_focus_analysis.json',
                'global_focus_trends.json'
            ]
        }