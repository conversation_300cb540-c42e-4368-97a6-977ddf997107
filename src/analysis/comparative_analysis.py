"""
Module: comparative_analysis
Description: Comprehensive comparative analysis of Indian private institutions in QS WUR
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- pandas
- numpy
- scipy
- sklearn
"""

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class IndianPrivateInstitutionsAnalyzer:
    """
    Comprehensive analyzer for Indian private institutions in QS World University Rankings
    with focus on competitive landscape and performance benchmarking.
    """
    
    def __init__(self, data_loader):
        """
        Initialize the Indian Private Institutions Analyzer.
        
        Parameters
        ----------
        data_loader : QSDataLoader
            Initialized data loader with QS WUR datasets
        """
        self.data_loader = data_loader
        self.indian_private_data = None
        self.performance_tiers = None
        self.clustering_results = None
        self.trend_analysis = None
        
    def load_indian_private_data(self) -> pd.DataFrame:
        """
        Load and prepare Indian private institutions data.
        
        Returns
        -------
        pd.DataFrame
            Indian private institutions data with additional analysis columns
        """
        self.indian_private_data = self.data_loader.get_indian_private_institutions()
        
        if self.indian_private_data.empty:
            logger.warning("No Indian private institutions data found")
            return pd.DataFrame()
        
        # Add performance indicators
        self.indian_private_data = self._add_performance_indicators(self.indian_private_data)
        
        logger.info(f"Loaded Indian private institutions data: {len(self.indian_private_data)} records")
        return self.indian_private_data
    
    def _add_performance_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add performance indicator columns to the dataset.
        
        Parameters
        ----------
        df : pd.DataFrame
            Base dataframe
            
        Returns
        -------
        pd.DataFrame
            DataFrame with additional performance indicators
        """
        df = df.copy()
        
        # Performance tier based on ranking
        df['Performance_Tier'] = df['Rank'].apply(self._categorize_performance_tier)
        
        # Consistency score (lower is better - based on ranking standard deviation)
        consistency_scores = df.groupby('Institution')['Rank'].std().fillna(0)
        df['Consistency_Score'] = df['Institution'].map(consistency_scores)
        
        # Improvement trajectory
        df = df.sort_values(['Institution', 'Year'])
        df['Rank_Change_YoY'] = df.groupby('Institution')['Rank'].diff()
        df['Is_Improving'] = df['Rank_Change_YoY'] < 0  # Negative change means better ranking
        
        # Years in ranking
        years_in_ranking = df.groupby('Institution')['Year'].count()
        df['Years_in_Ranking'] = df['Institution'].map(years_in_ranking)
        
        # Latest year performance
        latest_year = df['Year'].max()
        latest_performance = df[df['Year'] == latest_year].set_index('Institution')['Rank']
        df['Latest_Rank'] = df['Institution'].map(latest_performance)
        
        return df
    
    def _categorize_performance_tier(self, rank: float) -> str:
        """
        Categorize performance tier based on ranking.
        
        Parameters
        ----------
        rank : float
            QS World University Ranking
            
        Returns
        -------
        str
            Performance tier category
        """
        if pd.isna(rank):
            return 'Not Ranked'
        elif rank <= 500:
            return 'Tier 1 (Top 500)'
        elif rank <= 700:
            return 'Tier 2 (501-700)'
        elif rank <= 1000:
            return 'Tier 3 (701-1000)'
        else:
            return 'Tier 4 (1000+)'
    
    def analyze_competitive_landscape(self) -> Dict:
        """
        Analyze the competitive landscape of Indian private institutions.
        
        Returns
        -------
        Dict
            Comprehensive competitive landscape analysis
        """
        if self.indian_private_data is None:
            self.load_indian_private_data()
        
        landscape_analysis = {}
        
        # Market growth analysis
        landscape_analysis['market_growth'] = self._analyze_market_growth()
        
        # Performance distribution
        landscape_analysis['performance_distribution'] = self._analyze_performance_distribution()
        
        # Top performers analysis
        landscape_analysis['top_performers'] = self._analyze_top_performers()
        
        # Institutional characteristics
        landscape_analysis['institutional_characteristics'] = self._analyze_institutional_characteristics()
        
        # Trend analysis
        landscape_analysis['trend_analysis'] = self._analyze_trends()
        
        return landscape_analysis
    
    def _analyze_market_growth(self) -> Dict:
        """Analyze market growth and expansion of Indian private institutions."""
        growth_data = self.indian_private_data.groupby('Year').agg({
            'Institution': 'nunique',
            'Rank': ['mean', 'median', 'std'],
            'Overall_Score': 'mean'
        }).round(2)
        
        growth_data.columns = ['_'.join(col).strip() for col in growth_data.columns]
        growth_data = growth_data.reset_index()
        
        # Calculate growth rates
        growth_data['Institution_Growth_Rate'] = growth_data['Institution_nunique'].pct_change() * 100
        
        # Market maturity indicators
        first_year = growth_data['Year'].min()
        last_year = growth_data['Year'].max()
        
        growth_summary = {
            'institutions_first_year': int(growth_data[growth_data['Year'] == first_year]['Institution_nunique'].iloc[0]),
            'institutions_last_year': int(growth_data[growth_data['Year'] == last_year]['Institution_nunique'].iloc[0]),
            'total_growth_rate': ((growth_data[growth_data['Year'] == last_year]['Institution_nunique'].iloc[0] / 
                                 growth_data[growth_data['Year'] == first_year]['Institution_nunique'].iloc[0]) - 1) * 100,
            'avg_annual_growth': growth_data['Institution_Growth_Rate'].mean(),
            'yearly_breakdown': growth_data.to_dict('records')
        }
        
        return growth_summary
    
    def _analyze_performance_distribution(self) -> Dict:
        """Analyze performance distribution across institutions."""
        latest_year = self.indian_private_data['Year'].max()
        latest_data = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        # Performance tier distribution
        tier_distribution = latest_data['Performance_Tier'].value_counts().to_dict()
        
        # Ranking statistics
        ranking_stats = {
            'best_rank': int(latest_data['Rank'].min()),
            'worst_rank': int(latest_data['Rank'].max()),
            'median_rank': int(latest_data['Rank'].median()),
            'mean_rank': round(latest_data['Rank'].mean(), 1),
            'std_rank': round(latest_data['Rank'].std(), 1)
        }
        
        # Score distribution
        score_stats = {}
        score_columns = ['Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score']
        
        for col in score_columns:
            if col in latest_data.columns:
                score_stats[col] = {
                    'mean': round(latest_data[col].mean(), 2),
                    'median': round(latest_data[col].median(), 2),
                    'std': round(latest_data[col].std(), 2),
                    'min': round(latest_data[col].min(), 2),
                    'max': round(latest_data[col].max(), 2)
                }
        
        return {
            'tier_distribution': tier_distribution,
            'ranking_statistics': ranking_stats,
            'score_statistics': score_stats,
            'total_institutions': len(latest_data)
        }
    
    def _analyze_top_performers(self) -> Dict:
        """Analyze top performing institutions in detail."""
        latest_year = self.indian_private_data['Year'].max()
        latest_data = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        # Top 10 performers
        top_10 = latest_data.nsmallest(10, 'Rank')
        
        # Consistent performers (present in multiple years)
        consistent_performers = self.indian_private_data[
            self.indian_private_data['Years_in_Ranking'] >= 3
        ].groupby('Institution').agg({
            'Rank': ['mean', 'std', 'min', 'max'],
            'Years_in_Ranking': 'first',
            'Overall_Score': 'mean'
        }).round(2)
        
        consistent_performers.columns = ['_'.join(col).strip() for col in consistent_performers.columns]
        consistent_performers = consistent_performers.reset_index()
        consistent_performers = consistent_performers.sort_values('Rank_mean').head(10)
        
        # Most improved institutions
        improvement_analysis = self._calculate_improvement_rankings()
        
        return {
            'top_10_current': top_10[['Institution', 'Rank', 'Overall_Score']].to_dict('records'),
            'top_10_consistent': consistent_performers.to_dict('records'),
            'most_improved': improvement_analysis[:5],
            'fastest_growing': self._identify_fastest_growing()
        }
    
    def _calculate_improvement_rankings(self) -> List[Dict]:
        """Calculate institutions with the most improvement."""
        improvement_data = []
        
        for institution in self.indian_private_data['Institution'].unique():
            inst_data = self.indian_private_data[self.indian_private_data['Institution'] == institution]
            
            if len(inst_data) >= 2:
                inst_data = inst_data.sort_values('Year')
                first_rank = inst_data['Rank'].iloc[0]
                last_rank = inst_data['Rank'].iloc[-1]
                
                # Improvement is negative change (better ranking)
                improvement = first_rank - last_rank
                improvement_pct = (improvement / first_rank) * 100
                
                improvement_data.append({
                    'institution': institution,
                    'first_year': int(inst_data['Year'].iloc[0]),
                    'last_year': int(inst_data['Year'].iloc[-1]),
                    'first_rank': int(first_rank),
                    'last_rank': int(last_rank),
                    'improvement_positions': int(improvement),
                    'improvement_percentage': round(improvement_pct, 1),
                    'years_tracked': len(inst_data)
                })
        
        # Sort by improvement (descending)
        improvement_data.sort(key=lambda x: x['improvement_positions'], reverse=True)
        
        return improvement_data
    
    def _identify_fastest_growing(self) -> List[Dict]:
        """Identify institutions with fastest growth in rankings."""
        growth_data = []
        
        for institution in self.indian_private_data['Institution'].unique():
            inst_data = self.indian_private_data[self.indian_private_data['Institution'] == institution]
            
            if len(inst_data) >= 3:  # Need at least 3 years for trend analysis
                inst_data = inst_data.sort_values('Year')
                
                # Calculate linear regression slope (negative slope = improvement)
                years = inst_data['Year'].values
                ranks = inst_data['Rank'].values
                
                if len(years) > 1:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(years, ranks)
                    
                    growth_data.append({
                        'institution': institution,
                        'trend_slope': round(slope, 2),
                        'r_squared': round(r_value**2, 3),
                        'p_value': round(p_value, 3),
                        'years_tracked': len(inst_data),
                        'trend_direction': 'Improving' if slope < 0 else 'Declining'
                    })
        
        # Sort by slope (most negative = fastest improvement)
        growth_data.sort(key=lambda x: x['trend_slope'])
        
        return growth_data[:10]  # Top 10 fastest growing
    
    def _analyze_institutional_characteristics(self) -> Dict:
        """Analyze institutional characteristics and their correlation with performance."""
        latest_year = self.indian_private_data['Year'].max()
        latest_data = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        characteristics = {}
        
        # Size distribution
        if 'Size' in latest_data.columns:
            size_performance = latest_data.groupby('Size')['Rank'].agg(['mean', 'count']).round(1)
            characteristics['size_analysis'] = {
                'distribution': latest_data['Size'].value_counts().to_dict(),
                'performance_by_size': size_performance.to_dict('index')
            }
        
        # Focus distribution
        if 'Focus' in latest_data.columns:
            focus_performance = latest_data.groupby('Focus')['Rank'].agg(['mean', 'count']).round(1)
            characteristics['focus_analysis'] = {
                'distribution': latest_data['Focus'].value_counts().to_dict(),
                'performance_by_focus': focus_performance.to_dict('index')
            }
        
        # Research intensity
        if 'Research_Intensity' in latest_data.columns:
            research_performance = latest_data.groupby('Research_Intensity')['Rank'].agg(['mean', 'count']).round(1)
            characteristics['research_analysis'] = {
                'distribution': latest_data['Research_Intensity'].value_counts().to_dict(),
                'performance_by_research': research_performance.to_dict('index')
            }
        
        return characteristics
    
    def _analyze_trends(self) -> Dict:
        """Analyze trends in Indian private institutions performance."""
        trend_analysis = {}
        
        # Overall trend analysis
        yearly_stats = self.indian_private_data.groupby('Year').agg({
            'Rank': ['mean', 'median', 'std'],
            'Overall_Score': 'mean',
            'Academic_Reputation_Score': 'mean',
            'Employer_Reputation_Score': 'mean'
        }).round(2)
        
        yearly_stats.columns = ['_'.join(col).strip() for col in yearly_stats.columns]
        yearly_stats = yearly_stats.reset_index()
        
        trend_analysis['yearly_performance'] = yearly_stats.to_dict('records')
        
        # Calculate overall trend direction
        years = yearly_stats['Year'].values
        mean_ranks = yearly_stats['Rank_mean'].values
        
        if len(years) > 1:
            slope, _, r_value, p_value, _ = stats.linregress(years, mean_ranks)
            trend_analysis['overall_trend'] = {
                'direction': 'Improving' if slope < 0 else 'Declining',
                'slope': round(slope, 2),
                'r_squared': round(r_value**2, 3),
                'significance': 'Significant' if p_value < 0.05 else 'Not Significant'
            }
        
        # Identify emerging vs established institutions
        trend_analysis['institution_categories'] = self._categorize_institutions_by_presence()
        
        return trend_analysis
    
    def _categorize_institutions_by_presence(self) -> Dict:
        """Categorize institutions by their presence in rankings."""
        institution_presence = self.indian_private_data.groupby('Institution').agg({
            'Year': ['count', 'min', 'max'],
            'Rank': 'mean'
        }).round(1)
        
        institution_presence.columns = ['_'.join(col).strip() for col in institution_presence.columns]
        institution_presence = institution_presence.reset_index()
        
        # Categorize institutions
        categories = {
            'established': institution_presence[institution_presence['Year_count'] >= 4],
            'emerging': institution_presence[
                (institution_presence['Year_count'] >= 2) & 
                (institution_presence['Year_count'] < 4)
            ],
            'new_entrants': institution_presence[institution_presence['Year_count'] == 1]
        }
        
        return {
            'established_institutions': len(categories['established']),
            'emerging_institutions': len(categories['emerging']),
            'new_entrants': len(categories['new_entrants']),
            'details': {
                'established': categories['established'][['Institution', 'Year_count', 'Rank_mean']].to_dict('records'),
                'emerging': categories['emerging'][['Institution', 'Year_count', 'Rank_mean']].to_dict('records'),
                'new_entrants': categories['new_entrants'][['Institution', 'Year_count', 'Rank_mean']].to_dict('records')
            }
        }
    
    def perform_clustering_analysis(self, n_clusters: int = 4) -> Dict:
        """
        Perform clustering analysis to identify institutional groups.
        
        Parameters
        ----------
        n_clusters : int, default=4
            Number of clusters to create
            
        Returns
        -------
        Dict
            Clustering analysis results
        """
        if self.indian_private_data is None:
            self.load_indian_private_data()
        
        # Prepare data for clustering
        latest_year = self.indian_private_data['Year'].max()
        clustering_data = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        # Select features for clustering
        features = ['Rank', 'Academic_Reputation_Score', 'Employer_Reputation_Score', 
                   'Citations_per_Faculty_Score', 'International_Faculty_Score']
        
        # Filter for institutions with complete data
        complete_data = clustering_data.dropna(subset=features)
        
        if len(complete_data) < n_clusters:
            return {'error': 'Insufficient data for clustering analysis'}
        
        # Standardize features
        scaler = StandardScaler()
        X = scaler.fit_transform(complete_data[features])
        
        # Perform clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(X)
        
        # Add cluster labels to data
        complete_data = complete_data.copy()
        complete_data['Cluster'] = clusters
        
        # Analyze clusters
        cluster_analysis = {}
        for i in range(n_clusters):
            cluster_data = complete_data[complete_data['Cluster'] == i]
            
            cluster_analysis[f'Cluster_{i}'] = {
                'institutions': cluster_data['Institution'].tolist(),
                'size': len(cluster_data),
                'characteristics': {
                    'avg_rank': round(cluster_data['Rank'].mean(), 1),
                    'avg_academic_reputation': round(cluster_data['Academic_Reputation_Score'].mean(), 2),
                    'avg_employer_reputation': round(cluster_data['Employer_Reputation_Score'].mean(), 2),
                    'avg_citations': round(cluster_data['Citations_per_Faculty_Score'].mean(), 2),
                    'avg_international_faculty': round(cluster_data['International_Faculty_Score'].mean(), 2)
                }
            }
        
        # Store clustering results
        self.clustering_results = {
            'cluster_analysis': cluster_analysis,
            'cluster_data': complete_data,
            'features_used': features,
            'n_clusters': n_clusters
        }
        
        return self.clustering_results
    
    def generate_symbiosis_competitive_position(self, symbiosis_data: pd.DataFrame) -> Dict:
        """
        Generate Symbiosis competitive position within Indian private institutions.
        
        Parameters
        ----------
        symbiosis_data : pd.DataFrame
            Symbiosis performance data
            
        Returns
        -------
        Dict
            Symbiosis competitive position analysis
        """
        if self.indian_private_data is None:
            self.load_indian_private_data()
        
        # Get latest year data
        latest_year = max(symbiosis_data['Year'].max(), self.indian_private_data['Year'].max())
        
        # Symbiosis latest performance
        symbiosis_latest = symbiosis_data[symbiosis_data['Year'] == latest_year]
        
        # All Indian private institutions latest performance
        peers_latest = self.indian_private_data[self.indian_private_data['Year'] == latest_year]
        
        if symbiosis_latest.empty:
            return {'error': 'No Symbiosis data available for competitive analysis'}
        
        symbiosis_rank = symbiosis_latest['Rank'].iloc[0]
        
        # Calculate competitive position
        better_institutions = peers_latest[peers_latest['Rank'] < symbiosis_rank]
        worse_institutions = peers_latest[peers_latest['Rank'] > symbiosis_rank]
        
        position_analysis = {
            'symbiosis_rank': int(symbiosis_rank),
            'total_indian_private': len(peers_latest),
            'institutions_ranked_better': len(better_institutions),
            'institutions_ranked_worse': len(worse_institutions),
            'percentile_rank': round((len(better_institutions) / len(peers_latest)) * 100, 1),
            'market_position': f"{len(better_institutions) + 1} out of {len(peers_latest)}",
            'top_competitors': better_institutions.nsmallest(5, 'Rank')[['Institution', 'Rank']].to_dict('records'),
            'nearest_competitors': self._find_nearest_competitors(peers_latest, symbiosis_rank)
        }
        
        return position_analysis
    
    def _find_nearest_competitors(self, peers_data: pd.DataFrame, symbiosis_rank: int) -> Dict:
        """Find institutions with similar rankings to Symbiosis."""
        rank_diff = abs(peers_data['Rank'] - symbiosis_rank)
        peers_data = peers_data.copy()
        peers_data['Rank_Difference'] = rank_diff
        
        # Find 5 closest competitors (excluding Symbiosis itself)
        closest_competitors = peers_data[
            ~peers_data['Institution'].str.contains('Symbiosis', case=False, na=False)
        ].nsmallest(5, 'Rank_Difference')
        
        return {
            'above_symbiosis': closest_competitors[closest_competitors['Rank'] < symbiosis_rank][['Institution', 'Rank']].to_dict('records'),
            'below_symbiosis': closest_competitors[closest_competitors['Rank'] > symbiosis_rank][['Institution', 'Rank']].to_dict('records')
        }
    
    def export_analysis_results(self, output_directory: str) -> None:
        """
        Export all analysis results to files.
        
        Parameters
        ----------
        output_directory : str
            Directory to save analysis results
        """
        import os
        import json
        
        os.makedirs(output_directory, exist_ok=True)
        
        # Export competitive landscape analysis
        landscape_analysis = self.analyze_competitive_landscape()
        
        with open(os.path.join(output_directory, 'competitive_landscape_analysis.json'), 'w') as f:
            json.dump(landscape_analysis, f, indent=2)
        
        # Export processed data
        if self.indian_private_data is not None:
            self.indian_private_data.to_csv(
                os.path.join(output_directory, 'indian_private_institutions_analysis.csv'), 
                index=False
            )
        
        # Export clustering results if available
        if self.clustering_results is not None:
            with open(os.path.join(output_directory, 'clustering_analysis.json'), 'w') as f:
                json.dump({k: v for k, v in self.clustering_results.items() if k != 'cluster_data'}, f, indent=2)
            
            self.clustering_results['cluster_data'].to_csv(
                os.path.join(output_directory, 'institutional_clusters.csv'), 
                index=False
            )
        
        logger.info(f"Analysis results exported to {output_directory}")
    
    def generate_executive_summary(self) -> Dict:
        """
        Generate executive summary of Indian private institutions analysis.
        
        Returns
        -------
        Dict
            Executive summary with key findings and insights
        """
        if self.indian_private_data is None:
            self.load_indian_private_data()
        
        summary = {
            'market_overview': self._get_market_overview(),
            'performance_insights': self._get_performance_insights(),
            'competitive_dynamics': self._get_competitive_dynamics(),
            'strategic_implications': self._get_strategic_implications()
        }
        
        return summary
    
    def _get_market_overview(self) -> Dict:
        """Get market overview summary."""
        growth_analysis = self._analyze_market_growth()
        
        return {
            'total_institutions_2026': growth_analysis['institutions_last_year'],
            'market_growth_rate': round(growth_analysis['total_growth_rate'], 1),
            'market_maturity': 'Expanding' if growth_analysis['total_growth_rate'] > 50 else 'Mature',
            'key_insight': f"Indian private higher education representation in QS WUR grew by {growth_analysis['total_growth_rate']:.1f}% over 5 years"
        }
    
    def _get_performance_insights(self) -> Dict:
        """Get performance insights summary."""
        performance_dist = self._analyze_performance_distribution()
        top_performers = self._analyze_top_performers()
        
        return {
            'performance_range': f"{performance_dist['ranking_statistics']['best_rank']} - {performance_dist['ranking_statistics']['worst_rank']}",
            'median_rank': performance_dist['ranking_statistics']['median_rank'],
            'top_performer': top_performers['top_10_current'][0]['Institution'],
            'most_improved': top_performers['most_improved'][0]['institution'] if top_performers['most_improved'] else 'N/A',
            'key_insight': f"Top private institution ranks {performance_dist['ranking_statistics']['best_rank']} globally with median private institution rank at {performance_dist['ranking_statistics']['median_rank']}"
        }
    
    def _get_competitive_dynamics(self) -> Dict:
        """Get competitive dynamics summary."""
        characteristics = self._analyze_institutional_characteristics()
        
        size_insight = "Large institutions dominate" if 'L' in characteristics.get('size_analysis', {}).get('distribution', {}) else "Mixed size distribution"
        focus_insight = "Comprehensive focus preferred" if 'CO' in characteristics.get('focus_analysis', {}).get('distribution', {}) else "Varied focus areas"
        
        return {
            'size_distribution': characteristics.get('size_analysis', {}).get('distribution', {}),
            'focus_distribution': characteristics.get('focus_analysis', {}).get('distribution', {}),
            'size_insight': size_insight,
            'focus_insight': focus_insight
        }
    
    def _get_strategic_implications(self) -> List[str]:
        """Get strategic implications from the analysis."""
        implications = [
            "Private institutions are gaining significant presence in global rankings",
            "Employer reputation and industry connections drive competitive advantage",
            "Research output and international collaboration remain key differentiators",
            "Comprehensive universities show slight ranking advantages over focused institutions",
            "Consistent performance over time more valuable than single-year improvements"
        ]
        
        return implications