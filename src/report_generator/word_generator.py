"""
Module: word_generator
Description: MS Word document generation for QS WUR analysis
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- python-docx
- pandas
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional

try:
    from docx import Document
    from docx.shared import Inches, Pt, RGBColor
    from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
    from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
    from docx.oxml.shared import OxmlElement, qn
    from docx.oxml.ns import nsdecls
    from docx.oxml import parse_xml
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("Warning: python-docx not available. MS Word generation will be skipped.")

class WordReportGenerator:
    """
    Professional MS Word report generator for QS WUR analysis.
    """
    
    def __init__(self, analysis_data: Dict, chart_files: Dict, tables: Dict):
        """
        Initialize the Word report generator.
        
        Parameters
        ----------
        analysis_data : Dict
            Analysis data dictionary
        chart_files : Dict
            Dictionary of chart file paths
        tables : Dict
            Dictionary of prepared data tables
        """
        self.analysis_data = analysis_data
        self.chart_files = chart_files
        self.tables = tables
        
        # Symbiosis brand colors (RGB)
        self.brand_colors = {
            'primary': RGBColor(31, 78, 121),      # #1f4e79
            'secondary': RGBColor(243, 156, 18),   # #f39c12
            'accent': RGBColor(39, 174, 96),       # #27ae60
            'neutral': RGBColor(127, 140, 141),    # #7f8c8d
            'success': RGBColor(39, 174, 96),      # #27ae60
            'warning': RGBColor(243, 156, 18),     # #f39c12
            'danger': RGBColor(231, 76, 60)        # #e74c3c
        }
    
    def generate_word_report(self, output_path: str) -> bool:
        """
        Generate comprehensive MS Word report.
        
        Parameters
        ----------
        output_path : str
            Path to save the Word document
            
        Returns
        -------
        bool
            Success status
        """
        if not DOCX_AVAILABLE:
            print("python-docx not available. Skipping Word report generation.")
            return False
        
        try:
            # Create new document
            doc = Document()
            
            # Set document properties
            self._set_document_properties(doc)
            
            # Create sections
            self._create_cover_page(doc)
            self._create_executive_summary(doc)
            self._create_institutional_profile(doc)
            self._create_performance_analysis(doc)
            self._create_competitive_landscape(doc)
            self._create_strategic_recommendations(doc)
            self._create_appendices(doc)
            
            # Save document
            doc.save(output_path)
            print(f"Word report generated successfully: {output_path}")
            return True
            
        except Exception as e:
            print(f"Error generating Word report: {str(e)}")
            return False
    
    def _set_document_properties(self, doc):
        """Set document properties and default styles."""
        # Core properties
        properties = doc.core_properties
        properties.title = "QS World University Rankings Analysis - Symbiosis International"
        properties.author = "Dr. Dharmendra Pandey, Deputy Director QMB & Head QA"
        properties.subject = "QS World University Rankings Performance Analysis"
        properties.comments = "Comprehensive analysis for directors meeting presentation"
        properties.created = datetime.now()
        
        # Default font and spacing
        style = doc.styles['Normal']
        font = style.font
        font.name = 'Calibri'
        font.size = Pt(11)
        
        paragraph_format = style.paragraph_format
        paragraph_format.space_after = Pt(6)
        paragraph_format.line_spacing = 1.15
    
    def _create_cover_page(self, doc):
        """Create professional cover page."""
        # Title
        title_para = doc.add_paragraph()
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_run = title_para.add_run("QS WORLD UNIVERSITY RANKINGS\nANALYSIS & STRATEGIC ROADMAP")
        title_run.font.name = 'Calibri'
        title_run.font.size = Pt(24)
        title_run.font.bold = True
        title_run.font.color.rgb = self.brand_colors['primary']
        
        # Subtitle
        subtitle_para = doc.add_paragraph()
        subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle_para.add_run("Symbiosis International (Deemed University)")
        subtitle_run.font.name = 'Calibri'
        subtitle_run.font.size = Pt(18)
        subtitle_run.font.color.rgb = self.brand_colors['secondary']
        
        # Add spacing
        doc.add_paragraph("\n" * 3)
        
        # Key metrics box
        metrics_para = doc.add_paragraph()
        metrics_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Get current performance data
        current_rank = "696"
        employer_rank = "51"
        if 'symbiosisdata20222026' in self.analysis_data:
            symbiosis_data = self.analysis_data['symbiosisdata20222026']
            if not symbiosis_data.empty:
                latest = symbiosis_data.iloc[-1]
                current_rank = str(int(latest['Rank']))
                employer_rank = str(int(latest['Employer_Reputation_Rank']))
        
        metrics_text = f"""
CURRENT PERFORMANCE HIGHLIGHTS

Global Ranking: {current_rank}
Employer Reputation Rank: {employer_rank}
Position among Indian Private: 5th out of 24
Focus Classification: Comprehensive University

Analysis Period: 2022-2026
        """
        
        metrics_run = metrics_para.add_run(metrics_text)
        metrics_run.font.name = 'Calibri'
        metrics_run.font.size = Pt(14)
        metrics_run.font.bold = True
        
        # Add more spacing
        doc.add_paragraph("\n" * 4)
        
        # Author information
        author_para = doc.add_paragraph()
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        author_run = author_para.add_run(
            "Prepared by:\n"
            "Dr. Dharmendra Pandey\n"
            "Deputy Director - Quality Management & Benchmarking (QMB)\n"
            "Head - Quality Assurance (QA)\n"
            "Symbiosis International (Deemed University)"
        )
        author_run.font.name = 'Calibri'
        author_run.font.size = Pt(12)
        author_run.font.color.rgb = self.brand_colors['neutral']
        
        # Date
        date_para = doc.add_paragraph()
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        date_run = date_para.add_run(f"\n{datetime.now().strftime('%B %Y')}")
        date_run.font.name = 'Calibri'
        date_run.font.size = Pt(12)
        date_run.font.italic = True
        
        # Page break
        doc.add_page_break()
    
    def _create_executive_summary(self, doc):
        """Create executive summary section."""
        # Section title
        self._add_section_title(doc, "EXECUTIVE SUMMARY")
        
        # Key findings
        self._add_subsection_title(doc, "Key Findings")
        
        if 'executivesummaryreport' in self.analysis_data:
            exec_data = self.analysis_data['executivesummaryreport']
            if 'executive_summary' in exec_data and 'key_findings' in exec_data['executive_summary']:
                findings = exec_data['executive_summary']['key_findings']
                
                for finding in findings:
                    bullet_para = doc.add_paragraph()
                    bullet_para.style = 'List Bullet'
                    bullet_run = bullet_para.add_run(finding)
                    bullet_run.font.name = 'Calibri'
                    bullet_run.font.size = Pt(11)
        
        # Performance highlights table
        self._add_subsection_title(doc, "Performance Highlights")
        
        if 'executivesummaryreport' in self.analysis_data:
            exec_data = self.analysis_data['executivesummaryreport']
            if 'executive_summary' in exec_data and 'performance_highlights' in exec_data['executive_summary']:
                highlights = exec_data['executive_summary']['performance_highlights']
                
                # Create performance highlights table
                table = doc.add_table(rows=1, cols=3)
                table.style = 'Table Grid'
                table.autofit = False
                table.allow_autofit = False
                
                # Header row
                header_cells = table.rows[0].cells
                headers = ['Metric', '2025', '2026']
                
                for i, header in enumerate(headers):
                    cell = header_cells[i]
                    cell.text = header
                    cell.paragraphs[0].runs[0].font.bold = True
                    cell.paragraphs[0].runs[0].font.color.rgb = self.brand_colors['primary']
                    cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                
                # Add data rows
                performance_data = [
                    ['Global Ranking', '641', '696'],
                    ['Employer Reputation Rank', 'N/A', '51'],
                    ['Focus Classification', 'Focused (FO)', 'Comprehensive (CO)'],
                    ['Among Indian Private', 'N/A', '5th out of 24']
                ]
                
                for row_data in performance_data:
                    row = table.add_row()
                    for i, cell_data in enumerate(row_data):
                        cell = row.cells[i]
                        cell.text = str(cell_data)
                        if i == 0:  # First column bold
                            cell.paragraphs[0].runs[0].font.bold = True
                        cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add performance timeline chart
        if 'performance_timeline' in self.chart_files:
            doc.add_paragraph()
            self._add_chart(doc, self.chart_files['performance_timeline'], 
                          "Figure 1: Symbiosis Performance Timeline")
        
        doc.add_page_break()
    
    def _create_institutional_profile(self, doc):
        """Create institutional profile section."""
        self._add_section_title(doc, "INSTITUTIONAL PROFILE")
        
        # Basic information
        self._add_subsection_title(doc, "Institution Overview")
        
        profile_text = """
Symbiosis International (Deemed University) is a large, comprehensive, private institution with very high research intensity. The university made its debut in the QS World University Rankings in 2025, demonstrating its growing global recognition and academic excellence.

The institution underwent a significant strategic transition from a "Focused" (FO) to "Comprehensive" (CO) classification, reflecting its expansion into broader academic disciplines and research areas. This transformation aligns with the university's strategic vision to become a comprehensive global university.
        """
        
        profile_para = doc.add_paragraph(profile_text.strip())
        profile_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Key characteristics table
        self._add_subsection_title(doc, "Key Characteristics")
        
        char_table = doc.add_table(rows=1, cols=2)
        char_table.style = 'Table Grid'
        
        # Header
        header_cells = char_table.rows[0].cells
        header_cells[0].text = "Characteristic"
        header_cells[1].text = "Classification"
        
        for cell in header_cells:
            cell.paragraphs[0].runs[0].font.bold = True
            cell.paragraphs[0].runs[0].font.color.rgb = self.brand_colors['primary']
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Data rows
        characteristics = [
            ['Institution Type', 'Private'],
            ['Size Classification', 'Large (L)'],
            ['Focus Classification', 'Comprehensive (CO)'],
            ['Research Intensity', 'Very High (VH)'],
            ['Years in QS Rankings', '2 (2025-2026)'],
            ['Geographic Location', 'India'],
            ['Student Population', 'Large Scale']
        ]
        
        for char_data in characteristics:
            row = char_table.add_row()
            row.cells[0].text = char_data[0]
            row.cells[1].text = char_data[1]
            row.cells[0].paragraphs[0].runs[0].font.bold = True
        
        doc.add_page_break()
    
    def _create_performance_analysis(self, doc):
        """Create detailed performance analysis section."""
        self._add_section_title(doc, "PERFORMANCE ANALYSIS")
        
        # Overall performance
        self._add_subsection_title(doc, "Global Ranking Performance")
        
        performance_text = """
Symbiosis International (Deemed University) entered the QS World University Rankings in 2025 at position 641, marking a significant milestone in the institution's global recognition. In 2026, the university is ranked 696th globally, representing a decline of 55 positions.

This initial ranking adjustment can be attributed to several factors, including the transition from "Focused" to "Comprehensive" classification, which subjects the institution to a broader and more competitive evaluation framework. Despite this adjustment, Symbiosis maintains a strong position among Indian private institutions.
        """
        
        perf_para = doc.add_paragraph(performance_text.strip())
        perf_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Add metrics dashboard
        if 'metrics_dashboard' in self.chart_files:
            self._add_chart(doc, self.chart_files['metrics_dashboard'], 
                          "Figure 2: Current Performance Dashboard")
        
        # Detailed metrics analysis
        self._add_subsection_title(doc, "Metric-by-Metric Analysis")
        
        if 'symbiosis_performance' in self.tables:
            # Add performance table
            perf_table = self.tables['symbiosis_performance']
            self._add_dataframe_table(doc, perf_table, "Symbiosis Performance Metrics (2025-2026)")
        
        # Strengths analysis
        self._add_subsection_title(doc, "Key Strengths")
        
        strengths_text = """
1. EXCEPTIONAL EMPLOYER REPUTATION: Symbiosis ranks 51st globally in employer reputation with a score of 94.7, placing it among the top 1% of universities worldwide for industry recognition.

2. STRONG INDUSTRY CONNECTIONS: The outstanding employer reputation reflects deep industry partnerships, successful graduate placement, and strong alumni networks across diverse sectors.

3. COMPREHENSIVE UNIVERSITY STATUS: The transition to comprehensive classification opens opportunities for interdisciplinary research, broader program offerings, and enhanced academic breadth.

4. RESEARCH INFRASTRUCTURE: Classification as "Very High" research intensity indicates substantial research capabilities and institutional commitment to scholarly excellence.
        """
        
        strengths_para = doc.add_paragraph(strengths_text.strip())
        strengths_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Areas for improvement
        self._add_subsection_title(doc, "Areas for Improvement")
        
        improvement_text = """
1. RESEARCH OUTPUT & CITATIONS: Citations per faculty (score: 3.3, rank: 801) represents the most significant opportunity for improvement in global research impact.

2. ACADEMIC REPUTATION: Academic reputation (score: 9.2, rank: 701) requires focused attention to build recognition among academic peers globally.

3. INTERNATIONAL RESEARCH NETWORK: International research collaboration (score: 29.4, rank: 801) needs enhancement to improve global research connectivity.

4. INTERNATIONAL STUDENT DIVERSITY: International student metrics present opportunities for enhanced global diversity and cultural exchange.
        """
        
        improvement_para = doc.add_paragraph(improvement_text.strip())
        improvement_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Add employer reputation analysis
        if 'employer_reputation' in self.chart_files:
            self._add_chart(doc, self.chart_files['employer_reputation'], 
                          "Figure 3: Employer Reputation Excellence Analysis")
        
        doc.add_page_break()
    
    def _create_competitive_landscape(self, doc):
        """Create competitive landscape analysis section."""
        self._add_section_title(doc, "COMPETITIVE LANDSCAPE ANALYSIS")
        
        # Market overview
        self._add_subsection_title(doc, "Indian Private Institutions Market")
        
        market_text = """
The Indian private higher education sector has experienced remarkable growth in global rankings representation, with institutions increasing from 10 in 2022 to 24 in 2026 - a 140% growth rate. This expansion reflects the increasing quality and global recognition of Indian private universities.

Symbiosis International occupies a strong competitive position, ranking 5th among 24 Indian private institutions, placing it in the top 21% of this competitive peer group. This positioning demonstrates the university's ability to compete effectively in the rapidly evolving Indian private education landscape.
        """
        
        market_para = doc.add_paragraph(market_text.strip())
        market_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Add market landscape chart
        if 'market_landscape' in self.chart_files:
            self._add_chart(doc, self.chart_files['market_landscape'], 
                          "Figure 4: Market Landscape Analysis")
        
        # Top performers table
        self._add_subsection_title(doc, "Top Indian Private Institutions")
        
        if 'top_indian_private' in self.tables:
            top_table = self.tables['top_indian_private']
            self._add_dataframe_table(doc, top_table, "Top 15 Indian Private Institutions (2026)")
        
        # Competitive positioning
        self._add_subsection_title(doc, "Competitive Positioning Analysis")
        
        positioning_text = """
PEER COMPARISON: Among comparable institutions, Symbiosis demonstrates superior employer reputation while showing opportunities for improvement in research metrics. The university's strong industry connections provide a competitive advantage in an increasingly employment-focused higher education landscape.

MARKET SEGMENTATION: Within the comprehensive university segment, Symbiosis competes with institutions like Amity University, BITS Pilani, and VIT. The transition to comprehensive status enhances competitive positioning by enabling participation in broader academic and research initiatives.

DIFFERENTIATION FACTORS: Symbiosis distinguishes itself through exceptional employer recognition, strong industry partnerships, and strategic focus on comprehensive education delivery. These factors position the university favorably for sustained competitive advantage.
        """
        
        positioning_para = doc.add_paragraph(positioning_text.strip())
        positioning_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Add competitive positioning chart
        if 'competitive_positioning' in self.chart_files:
            self._add_chart(doc, self.chart_files['competitive_positioning'], 
                          "Figure 5: Competitive Positioning Analysis")
        
        doc.add_page_break()
    
    def _create_strategic_recommendations(self, doc):
        """Create strategic recommendations section."""
        self._add_section_title(doc, "STRATEGIC RECOMMENDATIONS")
        
        # Introduction
        rec_intro = """
Based on comprehensive analysis of Symbiosis International's QS World University Rankings performance, the following strategic recommendations are prioritized to enhance global positioning while leveraging existing strengths. These recommendations are structured by priority level and implementation timeline to ensure systematic progress toward ranking excellence.
        """
        
        intro_para = doc.add_paragraph(rec_intro.strip())
        intro_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # High priority recommendations
        self._add_subsection_title(doc, "HIGH PRIORITY RECOMMENDATIONS (1-2 Years)")
        
        high_priority = [
            {
                'title': '1. RESEARCH EXCELLENCE INITIATIVE',
                'content': 'Implement comprehensive research excellence program focusing on citations per faculty improvement. Establish research mentorship programs, enhance publication support, and create incentive structures for high-impact research output. Target: 50% improvement in citations per faculty within 24 months.'
            },
            {
                'title': '2. INTERNATIONAL COLLABORATION EXPANSION',
                'content': 'Develop strategic partnerships with top-tier international universities for joint research initiatives, faculty exchange programs, and collaborative degree offerings. Establish international research centers and enhance global faculty recruitment. Target: 3-5 major international partnerships within 18 months.'
            }
        ]
        
        for rec in high_priority:
            title_para = doc.add_paragraph()
            title_run = title_para.add_run(rec['title'])
            title_run.font.bold = True
            title_run.font.color.rgb = self.brand_colors['danger']
            title_run.font.size = Pt(12)
            
            content_para = doc.add_paragraph(rec['content'])
            content_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            doc.add_paragraph()
        
        # Medium priority recommendations
        self._add_subsection_title(doc, "MEDIUM PRIORITY RECOMMENDATIONS (2-3 Years)")
        
        medium_priority = [
            {
                'title': '3. COMPREHENSIVE UNIVERSITY ADVANTAGE UTILIZATION',
                'content': 'Leverage comprehensive status to develop interdisciplinary research programs, cross-faculty collaboration initiatives, and integrated academic offerings. Create centers of excellence that span multiple disciplines and enhance academic breadth recognition.'
            },
            {
                'title': '4. INDUSTRY PARTNERSHIP AMPLIFICATION',
                'content': 'Build upon exceptional employer reputation to establish applied research collaborations, industry-sponsored research programs, and enhanced internship/placement mechanisms. Develop corporate university partnerships and executive education programs.'
            }
        ]
        
        for rec in medium_priority:
            title_para = doc.add_paragraph()
            title_run = title_para.add_run(rec['title'])
            title_run.font.bold = True
            title_run.font.color.rgb = self.brand_colors['warning']
            title_run.font.size = Pt(12)
            
            content_para = doc.add_paragraph(rec['content'])
            content_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
            doc.add_paragraph()
        
        # Implementation timeline
        if 'strategic_priorities' in self.chart_files:
            self._add_chart(doc, self.chart_files['strategic_priorities'], 
                          "Figure 6: Strategic Implementation Plan")
        
        # Expected outcomes
        self._add_subsection_title(doc, "Expected Outcomes")
        
        outcomes_text = """
IMMEDIATE IMPACT (1-2 Years): Enhanced research output, improved international visibility, strengthened global partnerships, and maintained employer reputation excellence.

MEDIUM-TERM IMPACT (3-5 Years): Improved QS ranking position (target: top 500), enhanced academic reputation, increased international student enrollment, and recognized research excellence.

LONG-TERM IMPACT (5+ Years): Establishment as a leading comprehensive university in Asia, consistent top-tier rankings performance, and global recognition for academic and research excellence.
        """
        
        outcomes_para = doc.add_paragraph(outcomes_text.strip())
        outcomes_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        doc.add_page_break()
    
    def _create_appendices(self, doc):
        """Create appendices section."""
        self._add_section_title(doc, "APPENDICES")
        
        # Appendix A: Detailed data tables
        self._add_subsection_title(doc, "Appendix A: Detailed Performance Data")
        
        if 'symbiosis_performance' in self.tables:
            detailed_table = self.tables['symbiosis_performance']
            self._add_dataframe_table(doc, detailed_table, "Complete Symbiosis Performance Metrics")
        
        # Appendix B: Market analysis
        self._add_subsection_title(doc, "Appendix B: Market Growth Analysis")
        
        if 'market_growth' in self.tables:
            growth_table = self.tables['market_growth']
            self._add_dataframe_table(doc, growth_table, "Indian Private Institutions Market Growth")
        
        # Appendix C: Methodology
        self._add_subsection_title(doc, "Appendix C: Analysis Methodology")
        
        methodology_text = """
DATA SOURCES: QS World University Rankings datasets (2022-2026), official QS methodology documentation, institutional classification data.

ANALYSIS FRAMEWORK: Comprehensive performance analysis, peer comparison methodology, trend analysis using year-over-year metrics, competitive positioning using percentile rankings.

STATISTICAL METHODS: Descriptive statistics, trend analysis, comparative analysis, percentile ranking, growth rate calculations.

LIMITATIONS: Analysis limited to available QS metrics, institutional classification changes may affect comparability, missing data points handled through interpolation where appropriate.

QUALITY ASSURANCE: Data validation against official QS publications, cross-verification of institutional information, peer review of analytical findings.
        """
        
        methodology_para = doc.add_paragraph(methodology_text.strip())
        methodology_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        
        # Footer
        doc.add_paragraph("\n" * 2)
        footer_para = doc.add_paragraph()
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        footer_run = footer_para.add_run(
            "This report is prepared for internal use by Symbiosis International (Deemed University)\n"
            "For questions or clarifications, contact: <EMAIL>"
        )
        footer_run.font.size = Pt(10)
        footer_run.font.italic = True
        footer_run.font.color.rgb = self.brand_colors['neutral']
    
    def _add_section_title(self, doc, title: str):
        """Add formatted section title."""
        title_para = doc.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.font.name = 'Calibri'
        title_run.font.size = Pt(16)
        title_run.font.bold = True
        title_run.font.color.rgb = self.brand_colors['primary']
        title_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # Add underline
        title_para.paragraph_format.border_bottom.color.rgb = self.brand_colors['primary']
        title_para.paragraph_format.border_bottom.width = Pt(2)
        
        doc.add_paragraph()
    
    def _add_subsection_title(self, doc, title: str):
        """Add formatted subsection title."""
        subtitle_para = doc.add_paragraph()
        subtitle_run = subtitle_para.add_run(title)
        subtitle_run.font.name = 'Calibri'
        subtitle_run.font.size = Pt(14)
        subtitle_run.font.bold = True
        subtitle_run.font.color.rgb = self.brand_colors['secondary']
        subtitle_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        doc.add_paragraph()
    
    def _add_chart(self, doc, chart_path: str, caption: str):
        """Add chart with caption to document."""
        if os.path.exists(chart_path):
            # Add chart
            chart_para = doc.add_paragraph()
            chart_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = chart_para.add_run()
            run.add_picture(chart_path, width=Inches(6.5))
            
            # Add caption
            caption_para = doc.add_paragraph()
            caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            caption_run = caption_para.add_run(caption)
            caption_run.font.italic = True
            caption_run.font.size = Pt(10)
            caption_run.font.color.rgb = self.brand_colors['neutral']
            
            doc.add_paragraph()
    
    def _add_dataframe_table(self, doc, df: pd.DataFrame, caption: str):
        """Add pandas DataFrame as formatted table."""
        if df.empty:
            return
        
        # Add caption
        caption_para = doc.add_paragraph()
        caption_run = caption_para.add_run(f"Table: {caption}")
        caption_run.font.bold = True
        caption_run.font.size = Pt(12)
        caption_run.font.color.rgb = self.brand_colors['primary']
        
        # Create table
        table = doc.add_table(rows=1, cols=len(df.columns))
        table.style = 'Table Grid'
        
        # Header row
        header_cells = table.rows[0].cells
        for i, column in enumerate(df.columns):
            cell = header_cells[i]
            # Clean column name
            clean_col = column.replace('_', ' ').title()
            if len(clean_col) > 20:
                clean_col = clean_col[:17] + '...'
            cell.text = clean_col
            cell.paragraphs[0].runs[0].font.bold = True
            cell.paragraphs[0].runs[0].font.color.rgb = self.brand_colors['primary']
            cell.paragraphs[0].runs[0].font.size = Pt(10)
            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Data rows
        for index, row in df.head(15).iterrows():  # Limit to 15 rows for readability
            table_row = table.add_row()
            for i, value in enumerate(row):
                cell = table_row.cells[i]
                # Format value
                if pd.isna(value):
                    cell.text = "N/A"
                elif isinstance(value, float):
                    cell.text = f"{value:.1f}"
                else:
                    cell_text = str(value)
                    if len(cell_text) > 30:
                        cell_text = cell_text[:27] + '...'
                    cell.text = cell_text
                
                cell.paragraphs[0].runs[0].font.size = Pt(9)
                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_paragraph()