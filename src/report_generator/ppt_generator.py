"""
Module: ppt_generator
Description: PowerPoint presentation generation for QS WUR analysis
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- python-pptx
- pandas
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional

try:
    from pptx import Presentation
    from pptx.util import Inches, Pt
    from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
    from pptx.dml.color import RGBColor
    from pptx.enum.shapes import MSO_SHAPE
    from pptx.enum.dml import MSO_THEME_COLOR
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False
    print("Warning: python-pptx not available. PowerPoint generation will be skipped.")

class PowerPointGenerator:
    """
    Professional PowerPoint presentation generator for QS WUR analysis.
    """
    
    def __init__(self, analysis_data: Dict, chart_files: Dict, tables: Dict):
        """
        Initialize the PowerPoint generator.
        
        Parameters
        ----------
        analysis_data : Dict
            Analysis data dictionary
        chart_files : Dict
            Dictionary of chart file paths
        tables : Dict
            Dictionary of prepared data tables
        """
        self.analysis_data = analysis_data
        self.chart_files = chart_files
        self.tables = tables
        
        # Symbiosis brand colors (RGB values 0-255)
        self.brand_colors = {
            'primary': (31, 78, 121),      # #1f4e79
            'secondary': (243, 156, 18),   # #f39c12
            'accent': (39, 174, 96),       # #27ae60
            'neutral': (127, 140, 141),    # #7f8c8d
            'success': (39, 174, 96),      # #27ae60
            'warning': (243, 156, 18),     # #f39c12
            'danger': (231, 76, 60),       # #e74c3c
            'light': (236, 240, 241),      # #ecf0f1
            'white': (255, 255, 255),      # #ffffff
            'dark': (44, 62, 80)           # #2c3e50
        }
    
    def generate_presentation(self, output_path: str) -> bool:
        """
        Generate comprehensive PowerPoint presentation.
        
        Parameters
        ----------
        output_path : str
            Path to save the PowerPoint presentation
            
        Returns
        -------
        bool
            Success status
        """
        if not PPTX_AVAILABLE:
            print("python-pptx not available. Skipping PowerPoint presentation generation.")
            return False
        
        try:
            # Create new presentation
            prs = Presentation()
            
            # Set slide size to widescreen
            prs.slide_width = Inches(13.33)
            prs.slide_height = Inches(7.5)
            
            # Create slides
            self._create_title_slide(prs)
            self._create_agenda_slide(prs)
            self._create_executive_summary_slides(prs)
            self._create_institutional_journey_slides(prs)
            self._create_performance_analysis_slides(prs)
            self._create_competitive_intelligence_slides(prs)
            self._create_strategic_roadmap_slides(prs)
            self._create_appendix_slides(prs)
            
            # Save presentation
            prs.save(output_path)
            print(f"PowerPoint presentation generated successfully: {output_path}")
            return True
            
        except Exception as e:
            print(f"Error generating PowerPoint presentation: {str(e)}")
            return False
    
    def _create_title_slide(self, prs):
        """Create professional title slide."""
        slide_layout = prs.slide_layouts[6]  # Blank layout
        slide = prs.slides.add_slide(slide_layout)
        
        # Background color
        background = slide.background
        fill = background.fill
        fill.solid()
        fill.fore_color.rgb = RGBColor(*self.brand_colors['white'])
        
        # Main title
        title_left = Inches(1)
        title_top = Inches(1.5)
        title_width = Inches(11.33)
        title_height = Inches(1.5)
        
        title_box = slide.shapes.add_textbox(title_left, title_top, title_width, title_height)
        title_frame = title_box.text_frame
        title_frame.clear()
        
        title_para = title_frame.paragraphs[0]
        title_para.text = "QS WORLD UNIVERSITY RANKINGS"
        title_para.font.name = 'Calibri'
        title_para.font.size = Pt(36)
        title_para.font.bold = True
        title_para.font.color.rgb = RGBColor(*self.brand_colors['primary'])
        title_para.alignment = PP_ALIGN.CENTER
        
        # Subtitle
        subtitle_box = slide.shapes.add_textbox(title_left, Inches(3), title_width, Inches(0.8))
        subtitle_frame = subtitle_box.text_frame
        subtitle_para = subtitle_frame.paragraphs[0]
        subtitle_para.text = "ANALYSIS & STRATEGIC ROADMAP"
        subtitle_para.font.name = 'Calibri'
        subtitle_para.font.size = Pt(24)
        subtitle_para.font.color.rgb = RGBColor(*self.brand_colors['secondary'])
        subtitle_para.alignment = PP_ALIGN.CENTER
        
        # Institution name
        inst_box = slide.shapes.add_textbox(title_left, Inches(4), title_width, Inches(0.6))
        inst_frame = inst_box.text_frame
        inst_para = inst_frame.paragraphs[0]
        inst_para.text = "Symbiosis International (Deemed University)"
        inst_para.font.name = 'Calibri'
        inst_para.font.size = Pt(20)
        inst_para.font.italic = True
        inst_para.font.color.rgb = RGBColor(*self.brand_colors['accent'])
        inst_para.alignment = PP_ALIGN.CENTER
        
        # Key metrics box
        metrics_left = Inches(3)
        metrics_top = Inches(4.8)
        metrics_width = Inches(7.33)
        metrics_height = Inches(1.5)
        
        metrics_shape = slide.shapes.add_shape(
            MSO_SHAPE.ROUNDED_RECTANGLE, metrics_left, metrics_top, metrics_width, metrics_height
        )
        metrics_shape.fill.solid()
        metrics_shape.fill.fore_color.rgb = RGBColor(*self.brand_colors['light'])
        metrics_shape.line.color.rgb = RGBColor(*self.brand_colors['primary'])
        
        # Get current performance data
        current_rank = "696"
        employer_rank = "51"
        if 'symbiosisdata20222026' in self.analysis_data:
            symbiosis_data = self.analysis_data['symbiosisdata20222026']
            if not symbiosis_data.empty:
                latest = symbiosis_data.iloc[-1]
                current_rank = str(int(latest['Rank']))
                employer_rank = str(int(latest['Employer_Reputation_Rank']))
        
        metrics_frame = metrics_shape.text_frame
        metrics_para = metrics_frame.paragraphs[0]
        metrics_para.text = f"Global Rank: {current_rank} | Employer Reputation: {employer_rank} | Indian Private: 5th/24"
        metrics_para.font.name = 'Calibri'
        metrics_para.font.size = Pt(16)
        metrics_para.font.bold = True
        metrics_para.font.color.rgb = RGBColor(*self.brand_colors['primary'])
        metrics_para.alignment = PP_ALIGN.CENTER
        
        # Author and date
        author_box = slide.shapes.add_textbox(Inches(9), Inches(6.5), Inches(4), Inches(0.8))
        author_frame = author_box.text_frame
        author_para = author_frame.paragraphs[0]
        author_para.text = f"Dr. Dharmendra Pandey\nDeputy Director QMB & Head QA\n{datetime.now().strftime('%B %Y')}"
        author_para.font.name = 'Calibri'
        author_para.font.size = Pt(12)
        author_para.font.color.rgb = RGBColor(*self.brand_colors['neutral'])
        author_para.alignment = PP_ALIGN.RIGHT
    
    def _create_agenda_slide(self, prs):
        """Create agenda slide."""
        slide_layout = prs.slide_layouts[1]  # Title and Content
        slide = prs.slides.add_slide(slide_layout)
        
        # Title
        title = slide.shapes.title
        title.text = "AGENDA"
        self._format_slide_title(title)
        
        # Content
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        agenda_items = [
            "Executive Summary",
            "Institutional Journey & Transformation",
            "Performance Deep Dive Analysis",
            "Competitive Intelligence",
            "Strategic Roadmap & Recommendations",
            "Implementation Timeline",
            "Q&A Discussion"
        ]
        
        for i, item in enumerate(agenda_items, 1):
            para = text_frame.add_paragraph()
            para.text = f"{i}. {item}"
            para.font.name = 'Calibri'
            para.font.size = Pt(20)
            para.font.color.rgb = RGBColor(*self.brand_colors['primary'])
            para.space_after = Pt(12)
    
    def _create_executive_summary_slides(self, prs):
        """Create executive summary slides."""
        # Slide 1: Key Findings
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        title = slide.shapes.title
        title.text = "EXECUTIVE SUMMARY: KEY FINDINGS"
        self._format_slide_title(title)
        
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        if 'executivesummaryreport' in self.analysis_data:
            exec_data = self.analysis_data['executivesummaryreport']
            if 'executive_summary' in exec_data and 'key_findings' in exec_data['executive_summary']:
                findings = exec_data['executive_summary']['key_findings']
                
                for finding in findings:
                    para = text_frame.add_paragraph()
                    para.text = f"• {finding}"
                    para.font.name = 'Calibri'
                    para.font.size = Pt(16)
                    para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
                    para.space_after = Pt(8)
        
        # Slide 2: Performance Dashboard
        slide2 = prs.slides.add_slide(prs.slide_layouts[6])  # Blank
        
        # Title
        title_box = slide2.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "PERFORMANCE DASHBOARD"
        self._format_slide_title_text(title_para)
        
        # Add dashboard chart
        if 'metrics_dashboard' in self.chart_files:
            self._add_chart_to_slide(slide2, self.chart_files['metrics_dashboard'], 
                                   Inches(1), Inches(1.2), Inches(11.33), Inches(5.8))
    
    def _create_institutional_journey_slides(self, prs):
        """Create institutional journey slides."""
        # Slide 1: Entry into Rankings
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        title = slide.shapes.title
        title.text = "INSTITUTIONAL JOURNEY: QS RANKINGS ENTRY"
        self._format_slide_title(title)
        
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        journey_points = [
            "2025: Debut entry at rank 641 globally",
            "Recognized as Large, Private, Very High Research institution", 
            "Initial classification: Focused (FO) university",
            "2026: Strategic transition to Comprehensive (CO) status",
            "Current position: 696 globally, 5th among Indian private",
            "Exceptional employer reputation: Global rank 51"
        ]
        
        for point in journey_points:
            para = text_frame.add_paragraph()
            para.text = f"• {point}"
            para.font.name = 'Calibri'
            para.font.size = Pt(18)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
            para.space_after = Pt(10)
        
        # Slide 2: Focus Change Impact
        slide2 = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide2.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "FOCUS CLASSIFICATION CHANGE: FO → CO"
        self._format_slide_title_text(title_para)
        
        # Add focus change chart
        if 'focus_change_impact' in self.chart_files:
            self._add_chart_to_slide(slide2, self.chart_files['focus_change_impact'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
    
    def _create_performance_analysis_slides(self, prs):
        """Create performance analysis slides."""
        # Slide 1: Overall Performance
        slide = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "PERFORMANCE ANALYSIS: TRAJECTORY & METRICS"
        self._format_slide_title_text(title_para)
        
        # Add performance timeline
        if 'performance_timeline' in self.chart_files:
            self._add_chart_to_slide(slide, self.chart_files['performance_timeline'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
        
        # Slide 2: Strengths Analysis
        slide2 = prs.slides.add_slide(prs.slide_layouts[2])  # Two Content
        title2 = slide2.shapes.title
        title2.text = "PERFORMANCE STRENGTHS & OPPORTUNITIES"
        self._format_slide_title(title2)
        
        # Left side - Strengths
        left_content = slide2.placeholders[1]
        left_frame = left_content.text_frame
        left_frame.clear()
        
        # Add strengths title
        strengths_para = left_frame.add_paragraph()
        strengths_para.text = "KEY STRENGTHS"
        strengths_para.font.name = 'Calibri'
        strengths_para.font.size = Pt(16)
        strengths_para.font.bold = True
        strengths_para.font.color.rgb = RGBColor(*self.brand_colors['success'])
        
        strengths = [
            "Exceptional Employer Reputation (Rank 51)",
            "Strong Industry Connections",
            "Comprehensive University Status", 
            "Very High Research Classification"
        ]
        
        for strength in strengths:
            para = left_frame.add_paragraph()
            para.text = f"✓ {strength}"
            para.font.name = 'Calibri'
            para.font.size = Pt(14)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
        
        # Right side - Opportunities
        right_content = slide2.placeholders[2]
        right_frame = right_content.text_frame
        right_frame.clear()
        
        # Add opportunities title
        opp_para = right_frame.add_paragraph()
        opp_para.text = "IMPROVEMENT OPPORTUNITIES"
        opp_para.font.name = 'Calibri'
        opp_para.font.size = Pt(16)
        opp_para.font.bold = True
        opp_para.font.color.rgb = RGBColor(*self.brand_colors['warning'])
        
        opportunities = [
            "Citations per Faculty (Rank 801)",
            "Academic Reputation (Rank 701)",
            "International Research Network",
            "International Student Diversity"
        ]
        
        for opp in opportunities:
            para = right_frame.add_paragraph()
            para.text = f"⚡ {opp}"
            para.font.name = 'Calibri'
            para.font.size = Pt(14)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
        
        # Slide 3: Employer Reputation Excellence
        slide3 = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide3.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "EMPLOYER REPUTATION EXCELLENCE"
        self._format_slide_title_text(title_para)
        
        # Add employer reputation chart
        if 'employer_reputation' in self.chart_files:
            self._add_chart_to_slide(slide3, self.chart_files['employer_reputation'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
    
    def _create_competitive_intelligence_slides(self, prs):
        """Create competitive intelligence slides."""
        # Slide 1: Market Landscape
        slide = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "COMPETITIVE INTELLIGENCE: MARKET LANDSCAPE"
        self._format_slide_title_text(title_para)
        
        # Add market landscape chart
        if 'market_landscape' in self.chart_files:
            self._add_chart_to_slide(slide, self.chart_files['market_landscape'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
        
        # Slide 2: Competitive Positioning
        slide2 = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide2.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "COMPETITIVE POSITIONING ANALYSIS"
        self._format_slide_title_text(title_para)
        
        # Add competitive positioning chart
        if 'competitive_positioning' in self.chart_files:
            self._add_chart_to_slide(slide2, self.chart_files['competitive_positioning'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
        
        # Slide 3: Key Insights
        slide3 = prs.slides.add_slide(prs.slide_layouts[1])
        title3 = slide3.shapes.title
        title3.text = "COMPETITIVE INSIGHTS"
        self._format_slide_title(title3)
        
        content3 = slide3.placeholders[1]
        text_frame3 = content3.text_frame
        text_frame3.clear()
        
        insights = [
            "140% growth in Indian private institutions representation (2022-2026)",
            "Symbiosis ranks 5th among 24 Indian private institutions (Top 21%)",
            "Strong positioning against established competitors",
            "Employer reputation provides significant competitive advantage",
            "Comprehensive status enhances competitive portfolio"
        ]
        
        for insight in insights:
            para = text_frame3.add_paragraph()
            para.text = f"🎯 {insight}"
            para.font.name = 'Calibri'
            para.font.size = Pt(16)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
            para.space_after = Pt(10)
    
    def _create_strategic_roadmap_slides(self, prs):
        """Create strategic roadmap slides."""
        # Slide 1: Strategic Priorities
        slide = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "STRATEGIC ROADMAP: PRIORITIES & IMPLEMENTATION"
        self._format_slide_title_text(title_para)
        
        # Add strategic priorities chart
        if 'strategic_priorities' in self.chart_files:
            self._add_chart_to_slide(slide, self.chart_files['strategic_priorities'],
                                   Inches(0.5), Inches(1.2), Inches(12.33), Inches(5.8))
        
        # Slide 2: High Priority Actions
        slide2 = prs.slides.add_slide(prs.slide_layouts[1])
        title2 = slide2.shapes.title
        title2.text = "HIGH PRIORITY RECOMMENDATIONS"
        self._format_slide_title(title2)
        
        content2 = slide2.placeholders[1]
        text_frame2 = content2.text_frame
        text_frame2.clear()
        
        # Add priority box for Research Excellence
        priority1_para = text_frame2.add_paragraph()
        priority1_para.text = "1. RESEARCH EXCELLENCE INITIATIVE"
        priority1_para.font.name = 'Calibri'
        priority1_para.font.size = Pt(18)
        priority1_para.font.bold = True
        priority1_para.font.color.rgb = RGBColor(*self.brand_colors['danger'])
        
        detail1_para = text_frame2.add_paragraph()
        detail1_para.text = "• Implement comprehensive research excellence program\n• Focus on citations per faculty improvement\n• Timeline: 1-2 years | Target: 50% improvement"
        detail1_para.font.name = 'Calibri'
        detail1_para.font.size = Pt(14)
        detail1_para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
        
        text_frame2.add_paragraph()  # Spacing
        
        # Add priority box for International Collaboration
        priority2_para = text_frame2.add_paragraph()
        priority2_para.text = "2. INTERNATIONAL COLLABORATION EXPANSION"
        priority2_para.font.name = 'Calibri'
        priority2_para.font.size = Pt(18)
        priority2_para.font.bold = True
        priority2_para.font.color.rgb = RGBColor(*self.brand_colors['danger'])
        
        detail2_para = text_frame2.add_paragraph()
        detail2_para.text = "• Develop strategic international partnerships\n• Joint research initiatives and faculty exchange\n• Timeline: 1-3 years | Target: 3-5 major partnerships"
        detail2_para.font.name = 'Calibri'
        detail2_para.font.size = Pt(14)
        detail2_para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
        
        # Slide 3: Expected Outcomes
        slide3 = prs.slides.add_slide(prs.slide_layouts[2])  # Two Content
        title3 = slide3.shapes.title
        title3.text = "EXPECTED OUTCOMES & TIMELINE"
        self._format_slide_title(title3)
        
        # Left side - Short-term
        left_content = slide3.placeholders[1]
        left_frame = left_content.text_frame
        left_frame.clear()
        
        short_title = left_frame.add_paragraph()
        short_title.text = "SHORT-TERM (1-2 Years)"
        short_title.font.name = 'Calibri'
        short_title.font.size = Pt(16)
        short_title.font.bold = True
        short_title.font.color.rgb = RGBColor(*self.brand_colors['success'])
        
        short_outcomes = [
            "Enhanced research output",
            "Improved international visibility",
            "Strengthened global partnerships",
            "Maintained employer excellence"
        ]
        
        for outcome in short_outcomes:
            para = left_frame.add_paragraph()
            para.text = f"• {outcome}"
            para.font.name = 'Calibri'
            para.font.size = Pt(14)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
        
        # Right side - Long-term
        right_content = slide3.placeholders[2]
        right_frame = right_content.text_frame
        right_frame.clear()
        
        long_title = right_frame.add_paragraph()
        long_title.text = "LONG-TERM (3-5 Years)"
        long_title.font.name = 'Calibri'
        long_title.font.size = Pt(16)
        long_title.font.bold = True
        long_title.font.color.rgb = RGBColor(*self.brand_colors['accent'])
        
        long_outcomes = [
            "Target: Top 500 global ranking",
            "Enhanced academic reputation",
            "Increased international enrollment",
            "Recognized research excellence"
        ]
        
        for outcome in long_outcomes:
            para = right_frame.add_paragraph()
            para.text = f"• {outcome}"
            para.font.name = 'Calibri'
            para.font.size = Pt(14)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
    
    def _create_appendix_slides(self, prs):
        """Create appendix slides."""
        # Slide 1: Data Sources
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        title = slide.shapes.title
        title.text = "APPENDIX: DATA SOURCES & METHODOLOGY"
        self._format_slide_title(title)
        
        content = slide.placeholders[1]
        text_frame = content.text_frame
        text_frame.clear()
        
        data_sources = [
            "QS World University Rankings datasets (2022-2026)",
            "Official QS methodology documentation",
            "Institutional classification data and profiles",
            "Peer comparison analysis using percentile rankings",
            "Statistical trend analysis and growth calculations",
            "Cross-validation with official QS publications"
        ]
        
        for source in data_sources:
            para = text_frame.add_paragraph()
            para.text = f"• {source}"
            para.font.name = 'Calibri'
            para.font.size = Pt(16)
            para.font.color.rgb = RGBColor(*self.brand_colors['dark'])
            para.space_after = Pt(8)
        
        # Slide 2: Contact Information
        slide2 = prs.slides.add_slide(prs.slide_layouts[6])
        
        # Title
        title_box = slide2.shapes.add_textbox(Inches(0.5), Inches(1), Inches(12.33), Inches(1))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = "THANK YOU"
        title_para.font.name = 'Calibri'
        title_para.font.size = Pt(48)
        title_para.font.bold = True
        title_para.font.color.rgb = RGBColor(*self.brand_colors['primary'])
        title_para.alignment = PP_ALIGN.CENTER
        
        # Subtitle
        subtitle_box = slide2.shapes.add_textbox(Inches(0.5), Inches(2.5), Inches(12.33), Inches(1))
        subtitle_frame = subtitle_box.text_frame
        subtitle_para = subtitle_frame.paragraphs[0]
        subtitle_para.text = "Questions & Discussion"
        subtitle_para.font.name = 'Calibri'
        subtitle_para.font.size = Pt(24)
        subtitle_para.font.color.rgb = RGBColor(*self.brand_colors['secondary'])
        subtitle_para.alignment = PP_ALIGN.CENTER
        
        # Contact info
        contact_box = slide2.shapes.add_textbox(Inches(2), Inches(4.5), Inches(9.33), Inches(2))
        contact_frame = contact_box.text_frame
        contact_para = contact_frame.paragraphs[0]
        contact_para.text = ("Dr. Dharmendra Pandey\n"
                           "Deputy Director - Quality Management & Benchmarking (QMB)\n"
                           "Head - Quality Assurance (QA)\n"
                           "Symbiosis International (Deemed University)\n\n"
                           "<EMAIL> | <EMAIL>")
        contact_para.font.name = 'Calibri'
        contact_para.font.size = Pt(16)
        contact_para.font.color.rgb = RGBColor(*self.brand_colors['neutral'])
        contact_para.alignment = PP_ALIGN.CENTER
    
    def _format_slide_title(self, title_shape):
        """Format slide title with consistent styling."""
        title_shape.text_frame.paragraphs[0].font.name = 'Calibri'
        title_shape.text_frame.paragraphs[0].font.size = Pt(28)
        title_shape.text_frame.paragraphs[0].font.bold = True
        title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(*self.brand_colors['primary'])
    
    def _format_slide_title_text(self, title_para):
        """Format slide title paragraph with consistent styling."""
        title_para.font.name = 'Calibri'
        title_para.font.size = Pt(28)
        title_para.font.bold = True
        title_para.font.color.rgb = RGBColor(*self.brand_colors['primary'])
        title_para.alignment = PP_ALIGN.CENTER
    
    def _add_chart_to_slide(self, slide, chart_path: str, left, top, width, height):
        """Add chart image to slide."""
        if os.path.exists(chart_path):
            slide.shapes.add_picture(chart_path, left, top, width, height)