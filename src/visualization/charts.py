"""
Module: charts
Description: Professional chart creation functions for QS WUR analysis
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- matplotlib
- seaborn
- pandas
- numpy
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

from .styling import (
    SYMBIOSIS_COLORS, COLOR_PALETTES, 
    apply_symbiosis_branding, format_ranking_axis,
    create_performance_colormap, get_institution_color,
    add_trend_arrow, create_legend_for_tiers,
    apply_professional_formatting, save_figure
)

class QSVisualizationSuite:
    """
    Comprehensive visualization suite for QS World University Rankings analysis.
    """
    
    def __init__(self, output_directory: str = None):
        """
        Initialize the visualization suite.
        
        Parameters
        ----------
        output_directory : str, optional
            Directory to save generated charts
        """
        self.output_directory = output_directory
        
    def create_symbiosis_performance_timeline(self, symbiosis_data: pd.DataFrame) -> plt.Figure:
        """
        Create timeline chart showing Symbiosis performance evolution.
        
        Parameters
        ----------
        symbiosis_data : pd.DataFrame
            Symbiosis performance data across years
            
        Returns
        -------
        plt.Figure
            Timeline chart figure
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), 
                                      gridspec_kw={'height_ratios': [2, 1]})
        
        # Main timeline - Ranking
        years = symbiosis_data['Year'].values
        ranks = symbiosis_data['Rank'].values
        
        ax1.plot(years, ranks, marker='o', linewidth=3, markersize=8,
                color=SYMBIOSIS_COLORS['primary'], label='QS World Ranking')
        
        # Add data labels
        for year, rank in zip(years, ranks):
            ax1.annotate(f'{int(rank)}', (year, rank), 
                        textcoords="offset points", xytext=(0,10), 
                        ha='center', fontweight='bold', fontsize=11)
        
        # Format ranking axis
        format_ranking_axis(ax1, axis='y', reverse=True)
        ax1.set_xlabel('Year', fontweight='bold')
        ax1.set_title('Symbiosis International (Deemed University)\nQS World University Rankings Performance', 
                     fontsize=16, fontweight='bold', pad=20)
        
        # Add focus change annotation if detected
        if len(symbiosis_data['Focus'].dropna().unique()) > 1:
            focus_change_year = symbiosis_data[symbiosis_data['Focus'] == 'CO']['Year'].min()
            if not pd.isna(focus_change_year):
                ax1.axvline(x=focus_change_year - 0.5, color=SYMBIOSIS_COLORS['warning'], 
                           linestyle='--', alpha=0.7, linewidth=2)
                ax1.text(focus_change_year - 0.5, ax1.get_ylim()[0] + 50, 
                        'Focus Change:\nFO → CO', ha='center', va='bottom',
                        bbox=dict(boxstyle="round,pad=0.3", facecolor=SYMBIOSIS_COLORS['warning'], 
                                alpha=0.3), fontsize=10, fontweight='bold')
        
        # Secondary chart - Key metrics
        if 'Employer_Reputation_Score' in symbiosis_data.columns:
            ax2.plot(years, symbiosis_data['Employer_Reputation_Score'], 
                    marker='s', linewidth=2, markersize=6, 
                    color=SYMBIOSIS_COLORS['success'], label='Employer Reputation')
        
        if 'Academic_Reputation_Score' in symbiosis_data.columns:
            ax2.plot(years, symbiosis_data['Academic_Reputation_Score'], 
                    marker='^', linewidth=2, markersize=6,
                    color=SYMBIOSIS_COLORS['secondary'], label='Academic Reputation')
        
        if 'Citations_per_Faculty_Score' in symbiosis_data.columns:
            ax2.plot(years, symbiosis_data['Citations_per_Faculty_Score'], 
                    marker='d', linewidth=2, markersize=6,
                    color=SYMBIOSIS_COLORS['accent'], label='Citations per Faculty')
        
        ax2.set_xlabel('Year', fontweight='bold')
        ax2.set_ylabel('Score', fontweight='bold')
        ax2.set_title('Key Performance Metrics', fontsize=14, fontweight='bold')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # Apply formatting
        for ax in [ax1, ax2]:
            apply_professional_formatting(ax)
        
        apply_symbiosis_branding(ax1, show_logo_placeholder=True)
        
        plt.tight_layout()
        
        if self.output_directory:
            save_figure(fig, 'symbiosis_performance_timeline', self.output_directory)
        
        return fig
    
    def create_indian_private_institutions_landscape(self, indian_private_data: pd.DataFrame) -> plt.Figure:
        """
        Create comprehensive landscape view of Indian private institutions.
        
        Parameters
        ----------
        indian_private_data : pd.DataFrame
            Indian private institutions data
            
        Returns
        -------
        plt.Figure
            Landscape chart figure
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Get latest year data
        latest_year = indian_private_data['Year'].max()
        latest_data = indian_private_data[indian_private_data['Year'] == latest_year]
        
        # Chart 1: Top 15 Indian Private Institutions
        top_15 = latest_data.nsmallest(15, 'Rank')
        
        colors = [get_institution_color(inst, highlight_symbiosis=True) for inst in top_15['Institution']]
        
        bars1 = ax1.barh(range(len(top_15)), top_15['Rank'], color=colors, alpha=0.8)
        ax1.set_yticks(range(len(top_15)))
        ax1.set_yticklabels([inst[:30] + '...' if len(inst) > 30 else inst 
                            for inst in top_15['Institution']], fontsize=9)
        ax1.set_xlabel('QS World University Ranking', fontweight='bold')
        ax1.set_title('Top 15 Indian Private Institutions (2026)', fontweight='bold')
        ax1.invert_yaxis()
        
        # Add rank labels
        for i, (bar, rank) in enumerate(zip(bars1, top_15['Rank'])):
            ax1.text(bar.get_width() + 10, bar.get_y() + bar.get_height()/2, 
                    f'{int(rank)}', va='center', fontweight='bold', fontsize=9)
        
        # Chart 2: Market Growth Over Time
        growth_data = indian_private_data.groupby('Year')['Institution'].nunique()
        
        ax2.plot(growth_data.index, growth_data.values, marker='o', 
                linewidth=3, markersize=8, color=SYMBIOSIS_COLORS['primary'])
        ax2.fill_between(growth_data.index, growth_data.values, alpha=0.3, 
                        color=SYMBIOSIS_COLORS['primary'])
        
        # Add growth rate annotations
        for i in range(1, len(growth_data)):
            growth_rate = ((growth_data.iloc[i] - growth_data.iloc[i-1]) / growth_data.iloc[i-1]) * 100
            if growth_rate > 0:
                ax2.annotate(f'+{growth_rate:.0f}%', 
                           (growth_data.index[i], growth_data.iloc[i]),
                           textcoords="offset points", xytext=(0,10), 
                           ha='center', fontweight='bold', fontsize=9,
                           color=SYMBIOSIS_COLORS['success'])
        
        ax2.set_xlabel('Year', fontweight='bold')
        ax2.set_ylabel('Number of Institutions', fontweight='bold')
        ax2.set_title('Market Growth: Indian Private Institutions in QS WUR', fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # Chart 3: Performance Distribution
        performance_categories = latest_data['Rank'].apply(self._categorize_rank)
        category_counts = performance_categories.value_counts()
        
        colors_pie = [COLOR_PALETTES['ranking_tiers'][cat] for cat in category_counts.index]
        
        wedges, texts, autotexts = ax3.pie(category_counts.values, labels=category_counts.index, 
                                          colors=colors_pie, autopct='%1.1f%%', startangle=90)
        
        ax3.set_title('Performance Distribution (2026)', fontweight='bold')
        
        # Make percentage text bold
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(10)
        
        # Chart 4: Focus vs Performance Analysis
        if 'Focus' in latest_data.columns:
            focus_performance = latest_data.groupby('Focus')['Rank'].agg(['mean', 'count'])
            focus_performance = focus_performance[focus_performance['count'] >= 2]  # Filter small groups
            
            bars4 = ax4.bar(focus_performance.index, focus_performance['mean'], 
                           color=COLOR_PALETTES['categorical'][:len(focus_performance)], alpha=0.8)
            
            # Add count labels on bars
            for bar, count in zip(bars4, focus_performance['count']):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20,
                        f'n={int(count)}', ha='center', va='bottom', fontweight='bold', fontsize=9)
            
            ax4.set_ylabel('Average Ranking', fontweight='bold')
            ax4.set_xlabel('Institutional Focus', fontweight='bold')
            ax4.set_title('Performance by Institutional Focus', fontweight='bold')
            ax4.invert_yaxis()
            
            # Add focus definitions
            focus_labels = {'FC': 'Full Comprehensive', 'CO': 'Comprehensive', 
                           'FO': 'Focused', 'SP': 'Specialist'}
            ax4_labels = [focus_labels.get(focus, focus) for focus in focus_performance.index]
            ax4.set_xticklabels(ax4_labels, rotation=45, ha='right')
        
        # Apply professional formatting
        for ax in [ax1, ax2, ax3, ax4]:
            apply_professional_formatting(ax, remove_spines=True)
        
        apply_symbiosis_branding(fig.suptitle('Indian Private Institutions in QS World University Rankings', 
                                            fontsize=18, fontweight='bold', y=0.98))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        if self.output_directory:
            save_figure(fig, 'indian_private_institutions_landscape', self.output_directory)
        
        return fig
    
    def create_competitive_positioning_chart(self, symbiosis_data: pd.DataFrame, 
                                           competitor_data: pd.DataFrame) -> plt.Figure:
        """
        Create competitive positioning chart comparing Symbiosis with key competitors.
        
        Parameters
        ----------
        symbiosis_data : pd.DataFrame
            Symbiosis performance data
        competitor_data : pd.DataFrame
            Competitor institutions data
            
        Returns
        -------
        plt.Figure
            Competitive positioning chart
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Get latest data
        latest_year = max(symbiosis_data['Year'].max(), competitor_data['Year'].max())
        symbiosis_latest = symbiosis_data[symbiosis_data['Year'] == latest_year]
        competitors_latest = competitor_data[competitor_data['Year'] == latest_year]
        
        # Chart 1: Ranking Comparison
        if not symbiosis_latest.empty:
            # Get top 10 competitors + Symbiosis
            top_competitors = competitors_latest.nsmallest(10, 'Rank')
            
            # Combine data
            comparison_data = pd.concat([symbiosis_latest, top_competitors])
            comparison_data = comparison_data.drop_duplicates(subset=['Institution'])
            comparison_data = comparison_data.sort_values('Rank')
            
            colors = [get_institution_color(inst, highlight_symbiosis=True) 
                     for inst in comparison_data['Institution']]
            
            bars = ax1.barh(range(len(comparison_data)), comparison_data['Rank'], 
                           color=colors, alpha=0.8)
            
            ax1.set_yticks(range(len(comparison_data)))
            ax1.set_yticklabels([inst[:25] + '...' if len(inst) > 25 else inst 
                               for inst in comparison_data['Institution']], fontsize=10)
            ax1.set_xlabel('QS World University Ranking', fontweight='bold')
            ax1.set_title('Competitive Positioning (2026)', fontweight='bold')
            ax1.invert_yaxis()
            
            # Add rank labels
            for bar, rank in zip(bars, comparison_data['Rank']):
                ax1.text(bar.get_width() + 15, bar.get_y() + bar.get_height()/2, 
                        f'{int(rank)}', va='center', fontweight='bold', fontsize=10)
            
            # Highlight Symbiosis
            symbiosis_idx = comparison_data[comparison_data['Institution'].str.contains('Symbiosis', na=False)].index
            if len(symbiosis_idx) > 0:
                symbiosis_pos = list(comparison_data.index).index(symbiosis_idx[0])
                ax1.axhline(y=symbiosis_pos, color=SYMBIOSIS_COLORS['primary'], 
                           linestyle='--', alpha=0.7, linewidth=2)
        
        # Chart 2: Multi-dimensional Performance Radar
        if not symbiosis_latest.empty and len(competitors_latest) > 0:
            # Select key metrics for radar chart
            metrics = ['Academic_Reputation_Score', 'Employer_Reputation_Score', 
                      'Citations_per_Faculty_Score', 'International_Faculty_Score',
                      'International_Students_Score']
            
            # Get Symbiosis scores
            symbiosis_scores = []
            metric_labels = []
            
            for metric in metrics:
                if metric in symbiosis_latest.columns:
                    score = symbiosis_latest[metric].iloc[0]
                    if not pd.isna(score):
                        symbiosis_scores.append(score)
                        metric_labels.append(metric.replace('_', ' ').replace('Score', '').strip())
            
            if len(symbiosis_scores) >= 3:  # Need at least 3 metrics for meaningful radar
                # Calculate competitor averages
                competitor_averages = []
                for metric in [m for m in metrics if m in symbiosis_latest.columns]:
                    if metric in competitors_latest.columns:
                        avg_score = competitors_latest[metric].mean()
                        if not pd.isna(avg_score):
                            competitor_averages.append(avg_score)
                
                # Create radar chart
                angles = np.linspace(0, 2*np.pi, len(symbiosis_scores), endpoint=False).tolist()
                angles += angles[:1]  # Complete the circle
                
                symbiosis_scores += symbiosis_scores[:1]  # Complete the circle
                competitor_averages += competitor_averages[:1]
                
                ax2.plot(angles, symbiosis_scores, 'o-', linewidth=2, markersize=6,
                        color=SYMBIOSIS_COLORS['primary'], label='Symbiosis')
                ax2.fill(angles, symbiosis_scores, alpha=0.25, color=SYMBIOSIS_COLORS['primary'])
                
                ax2.plot(angles, competitor_averages, 'o-', linewidth=2, markersize=6,
                        color=SYMBIOSIS_COLORS['secondary'], label='Competitor Average')
                ax2.fill(angles, competitor_averages, alpha=0.15, color=SYMBIOSIS_COLORS['secondary'])
                
                ax2.set_xticks(angles[:-1])
                ax2.set_xticklabels(metric_labels, fontsize=10)
                ax2.set_ylim(0, 100)
                ax2.set_title('Multi-dimensional Performance Comparison', fontweight='bold')
                ax2.legend(loc='upper right')
                ax2.grid(True, alpha=0.3)
        
        # Apply formatting
        for ax in [ax1, ax2]:
            apply_professional_formatting(ax)
        
        apply_symbiosis_branding(ax1)
        
        plt.tight_layout()
        
        if self.output_directory:
            save_figure(fig, 'competitive_positioning', self.output_directory)
        
        return fig
    
    def create_focus_change_impact_analysis(self, focus_analysis_data: Dict) -> plt.Figure:
        """
        Create visualization showing the impact of focus classification changes.
        
        Parameters
        ----------
        focus_analysis_data : Dict
            Focus change analysis results
            
        Returns
        -------
        plt.Figure
            Focus change impact analysis chart
        """
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Chart 1: Symbiosis Focus Change Timeline
        if 'timeline' in focus_analysis_data:
            timeline = focus_analysis_data['timeline']
            
            years = [entry['year'] for entry in timeline]
            ranks = [entry['rank'] for entry in timeline]
            focuses = [entry['focus'] for entry in timeline]
            
            # Color by focus
            focus_colors = {'FO': SYMBIOSIS_COLORS['warning'], 'CO': SYMBIOSIS_COLORS['primary']}
            colors = [focus_colors.get(focus, SYMBIOSIS_COLORS['neutral']) for focus in focuses]
            
            ax1.scatter(years, ranks, c=colors, s=100, alpha=0.8, edgecolors='black')
            ax1.plot(years, ranks, linewidth=2, alpha=0.6, color=SYMBIOSIS_COLORS['neutral'])
            
            # Add focus change annotation
            focus_change_year = None
            for i in range(1, len(timeline)):
                if timeline[i]['focus'] != timeline[i-1]['focus']:
                    focus_change_year = timeline[i]['year']
                    ax1.axvline(x=focus_change_year - 0.5, color=SYMBIOSIS_COLORS['danger'], 
                               linestyle='--', alpha=0.7, linewidth=2)
                    ax1.text(focus_change_year - 0.5, min(ranks) - 50, 
                            f'Focus Change\n{timeline[i-1]["focus"]} → {timeline[i]["focus"]}',
                            ha='center', va='top', fontweight='bold', fontsize=10,
                            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
                    break
            
            format_ranking_axis(ax1, axis='y', reverse=True)
            ax1.set_xlabel('Year', fontweight='bold')
            ax1.set_title('Symbiosis Focus Classification Change Impact', fontweight='bold')
            
            # Add legend for focus types
            legend_elements = [mpatches.Patch(color=focus_colors['FO'], label='Focused (FO)'),
                             mpatches.Patch(color=focus_colors['CO'], label='Comprehensive (CO)')]
            ax1.legend(handles=legend_elements, loc='best')
        
        # Chart 2: Performance Metrics Before/After Focus Change
        if 'detailed_metrics' in focus_analysis_data and 'transition_details' in focus_analysis_data['detailed_metrics']:
            transitions = focus_analysis_data['detailed_metrics']['transition_details']
            
            if transitions:
                transition = transitions[0]  # Focus on first transition
                metrics = transition['metric_changes']
                
                metric_names = []
                before_values = []
                after_values = []
                
                for metric, data in metrics.items():
                    if metric != 'rank':  # Handle rank separately due to inverse scale
                        metric_names.append(metric.replace('_', ' ').title())
                        before_values.append(data['before'])
                        after_values.append(data['after'])
                
                if metric_names:
                    x = np.arange(len(metric_names))
                    width = 0.35
                    
                    bars1 = ax2.bar(x - width/2, before_values, width, label='Before (FO)',
                                   color=SYMBIOSIS_COLORS['warning'], alpha=0.8)
                    bars2 = ax2.bar(x + width/2, after_values, width, label='After (CO)',
                                   color=SYMBIOSIS_COLORS['primary'], alpha=0.8)
                    
                    ax2.set_xlabel('Performance Metrics', fontweight='bold')
                    ax2.set_ylabel('Score', fontweight='bold')
                    ax2.set_title('Performance Metrics: Before vs After Focus Change', fontweight='bold')
                    ax2.set_xticks(x)
                    ax2.set_xticklabels(metric_names, rotation=45, ha='right')
                    ax2.legend()
                    
                    # Add value labels on bars
                    for bars in [bars1, bars2]:
                        for bar in bars:
                            height = bar.get_height()
                            if not pd.isna(height):
                                ax2.text(bar.get_x() + bar.get_width()/2., height,
                                        f'{height:.1f}', ha='center', va='bottom', fontsize=8)
        
        # Chart 3: Global Focus Type Performance Distribution
        if 'focus_definitions' in focus_analysis_data:
            # Sample data for global focus performance (would come from broader analysis)
            focus_performance = {
                'Full Comprehensive': {'avg_rank': 350, 'count': 120},
                'Comprehensive': {'avg_rank': 420, 'count': 85},
                'Focused': {'avg_rank': 480, 'count': 95},
                'Specialist': {'avg_rank': 520, 'count': 45}
            }
            
            focuses = list(focus_performance.keys())
            avg_ranks = [focus_performance[f]['avg_rank'] for f in focuses]
            counts = [focus_performance[f]['count'] for f in focuses]
            
            colors = [COLOR_PALETTES['categorical'][i] for i in range(len(focuses))]
            
            bars = ax3.bar(focuses, avg_ranks, color=colors, alpha=0.8)
            
            # Add count labels
            for bar, count in zip(bars, counts):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                        f'n={count}', ha='center', va='bottom', fontweight='bold', fontsize=9)
            
            ax3.set_ylabel('Average Global Ranking', fontweight='bold')
            ax3.set_xlabel('Institutional Focus Type', fontweight='bold')
            ax3.set_title('Global Performance by Focus Type', fontweight='bold')
            ax3.set_xticklabels(focuses, rotation=45, ha='right')
            ax3.invert_yaxis()
            
            # Highlight Comprehensive
            comp_idx = focuses.index('Comprehensive')
            bars[comp_idx].set_edgecolor(SYMBIOSIS_COLORS['primary'])
            bars[comp_idx].set_linewidth(3)
            
        # Chart 4: Strategic Implications
        if 'strategic_implications' in focus_analysis_data:
            implications = focus_analysis_data['strategic_implications'][:5]  # Top 5
            
            # Create text-based visualization of implications
            ax4.axis('off')
            ax4.set_title('Strategic Implications of Focus Change', fontweight='bold', pad=20)
            
            for i, implication in enumerate(implications):
                y_pos = 0.9 - (i * 0.18)
                
                # Add bullet point
                ax4.text(0.05, y_pos, '•', transform=ax4.transAxes, fontsize=14,
                        color=SYMBIOSIS_COLORS['primary'], fontweight='bold')
                
                # Add implication text (wrap long text)
                wrapped_text = self._wrap_text(implication, 60)
                ax4.text(0.1, y_pos, wrapped_text, transform=ax4.transAxes, fontsize=11,
                        va='top', ha='left', wrap=True)
        
        # Apply formatting
        for ax in [ax1, ax2, ax3]:
            apply_professional_formatting(ax)
        
        apply_symbiosis_branding(fig.suptitle('Focus Classification Change: Impact Analysis', 
                                            fontsize=18, fontweight='bold', y=0.98))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        if self.output_directory:
            save_figure(fig, 'focus_change_impact_analysis', self.output_directory)
        
        return fig
    
    def create_executive_dashboard(self, summary_data: Dict) -> plt.Figure:
        """
        Create executive dashboard with key performance indicators.
        
        Parameters
        ----------
        summary_data : Dict
            Executive summary data
            
        Returns
        -------
        plt.Figure
            Executive dashboard figure
        """
        fig = plt.figure(figsize=(20, 12))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
        
        # Key Metrics Cards (Top Row)
        metrics = [
            {'title': 'Current Global Rank', 'value': '696', 'change': '-55', 'color': 'warning'},
            {'title': 'Employer Reputation Rank', 'value': '51', 'change': '+20', 'color': 'success'},
            {'title': 'Years in Rankings', 'value': '2', 'change': 'New Entry', 'color': 'primary'},
            {'title': 'Among Private Indian', 'value': '6th', 'change': 'Top 25%', 'color': 'accent'}
        ]
        
        for i, metric in enumerate(metrics):
            ax = fig.add_subplot(gs[0, i])
            self._create_metric_card(ax, metric)
        
        # Main Charts (Middle and Bottom Rows)
        
        # Performance Timeline
        ax_timeline = fig.add_subplot(gs[1, :2])
        # Placeholder for timeline data
        years = [2025, 2026]
        ranks = [641, 696]
        
        ax_timeline.plot(years, ranks, marker='o', linewidth=4, markersize=12,
                        color=SYMBIOSIS_COLORS['primary'])
        format_ranking_axis(ax_timeline, axis='y', reverse=True)
        ax_timeline.set_title('Ranking Trajectory', fontweight='bold', fontsize=14)
        ax_timeline.grid(True, alpha=0.3)
        
        # Competitive Position
        ax_position = fig.add_subplot(gs[1, 2:])
        
        # Sample competitive data
        institutions = ['Shoolini Univ', 'Chandigarh Univ', 'BITS Pilani', 'VIT', 
                       'Symbiosis Intl', 'Thapar Inst']
        ranks_comp = [503, 575, 668, 691, 696, 771]
        colors_comp = [SYMBIOSIS_COLORS['secondary'] if inst != 'Symbiosis Intl' 
                      else SYMBIOSIS_COLORS['primary'] for inst in institutions]
        
        bars = ax_position.barh(institutions, ranks_comp, color=colors_comp, alpha=0.8)
        ax_position.set_xlabel('QS World Ranking', fontweight='bold')
        ax_position.set_title('Competitive Position vs Top Indian Private Universities', 
                             fontweight='bold', fontsize=14)
        ax_position.invert_yaxis()
        
        # Strengths and Opportunities
        ax_strengths = fig.add_subplot(gs[2, :2])
        
        strengths_data = {
            'Employer Reputation': 94.7,
            'International Faculty': 23.3,
            'Sustainability': 45.6,
            'Academic Reputation': 9.2,
            'Citations per Faculty': 3.3
        }
        
        metrics_names = list(strengths_data.keys())
        scores = list(strengths_data.values())
        colors_strengths = [SYMBIOSIS_COLORS['success'] if score > 50 else 
                           SYMBIOSIS_COLORS['warning'] if score > 20 else 
                           SYMBIOSIS_COLORS['danger'] for score in scores]
        
        bars_strengths = ax_strengths.bar(metrics_names, scores, color=colors_strengths, alpha=0.8)
        ax_strengths.set_ylabel('Score', fontweight='bold')
        ax_strengths.set_title('Performance Metrics Analysis', fontweight='bold', fontsize=14)
        ax_strengths.set_xticklabels(metrics_names, rotation=45, ha='right')
        
        # Add score labels
        for bar, score in zip(bars_strengths, scores):
            ax_strengths.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                            f'{score}', ha='center', va='bottom', fontweight='bold')
        
        # Strategic Priorities
        ax_priorities = fig.add_subplot(gs[2, 2:])
        ax_priorities.axis('off')
        ax_priorities.set_title('Strategic Priorities for Improvement', fontweight='bold', 
                               fontsize=14, pad=20)
        
        priorities = [
            '1. Enhance Research Output & Citations',
            '2. Strengthen Academic Reputation',
            '3. Expand International Research Network',
            '4. Leverage Comprehensive Status',
            '5. Build on Employer Recognition Strength'
        ]
        
        for i, priority in enumerate(priorities):
            y_pos = 0.9 - (i * 0.15)
            
            # Priority number circle
            circle = plt.Circle((0.05, y_pos), 0.03, transform=ax_priorities.transAxes,
                              color=SYMBIOSIS_COLORS['primary'], alpha=0.8)
            ax_priorities.add_patch(circle)
            ax_priorities.text(0.05, y_pos, str(i+1), transform=ax_priorities.transAxes,
                             ha='center', va='center', color='white', fontweight='bold', fontsize=12)
            
            # Priority text
            ax_priorities.text(0.12, y_pos, priority[2:], transform=ax_priorities.transAxes,
                             va='center', ha='left', fontsize=11, fontweight='bold')
        
        # Apply branding
        fig.suptitle('Symbiosis International (Deemed University)\nQS World University Rankings - Executive Dashboard', 
                    fontsize=20, fontweight='bold', y=0.97)
        
        # Add institutional footer
        fig.text(0.99, 0.01, 'Symbiosis International (Deemed University) - Quality Management & Benchmarking', 
                ha='right', va='bottom', fontsize=10, color=SYMBIOSIS_COLORS['neutral'], alpha=0.7)
        
        if self.output_directory:
            save_figure(fig, 'executive_dashboard', self.output_directory)
        
        return fig
    
    def _create_metric_card(self, ax, metric_data: Dict):
        """Create a metric card visualization."""
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # Background
        color_map = {
            'primary': SYMBIOSIS_COLORS['primary'],
            'success': SYMBIOSIS_COLORS['success'],
            'warning': SYMBIOSIS_COLORS['warning'],
            'accent': SYMBIOSIS_COLORS['accent']
        }
        
        bg_color = color_map.get(metric_data['color'], SYMBIOSIS_COLORS['primary'])
        rect = mpatches.Rectangle((0, 0), 1, 1, facecolor=bg_color, alpha=0.1, 
                                transform=ax.transAxes)
        ax.add_patch(rect)
        
        # Title
        ax.text(0.5, 0.8, metric_data['title'], ha='center', va='center', 
               fontsize=12, fontweight='bold', transform=ax.transAxes)
        
        # Value
        ax.text(0.5, 0.5, metric_data['value'], ha='center', va='center',
               fontsize=24, fontweight='bold', color=bg_color, transform=ax.transAxes)
        
        # Change
        change_color = SYMBIOSIS_COLORS['success'] if '+' in str(metric_data['change']) else SYMBIOSIS_COLORS['warning']
        ax.text(0.5, 0.2, metric_data['change'], ha='center', va='center',
               fontsize=10, fontweight='bold', color=change_color, transform=ax.transAxes)
    
    def _categorize_rank(self, rank: float) -> str:
        """Categorize ranking into performance tiers."""
        if pd.isna(rank):
            return 'Not Ranked'
        elif rank <= 100:
            return 'Top 100'
        elif rank <= 200:
            return 'Top 200'
        elif rank <= 500:
            return 'Top 500'
        elif rank <= 1000:
            return 'Top 1000'
        else:
            return 'Beyond 1000'
    
    def _wrap_text(self, text: str, width: int) -> str:
        """Wrap text to specified width."""
        import textwrap
        return '\n'.join(textwrap.wrap(text, width=width))
    
    def export_all_visualizations(self, data_dict: Dict) -> List[str]:
        """
        Export all visualizations using provided data.
        
        Parameters
        ----------
        data_dict : Dict
            Dictionary containing all required data for visualizations
            
        Returns
        -------
        List[str]
            List of created visualization files
        """
        created_files = []
        
        try:
            # Create Symbiosis timeline
            if 'symbiosis_data' in data_dict:
                fig1 = self.create_symbiosis_performance_timeline(data_dict['symbiosis_data'])
                created_files.append('symbiosis_performance_timeline')
                plt.close(fig1)
            
            # Create landscape view
            if 'indian_private_data' in data_dict:
                fig2 = self.create_indian_private_institutions_landscape(data_dict['indian_private_data'])
                created_files.append('indian_private_institutions_landscape')
                plt.close(fig2)
            
            # Create competitive positioning
            if 'symbiosis_data' in data_dict and 'competitor_data' in data_dict:
                fig3 = self.create_competitive_positioning_chart(
                    data_dict['symbiosis_data'], data_dict['competitor_data'])
                created_files.append('competitive_positioning')
                plt.close(fig3)
            
            # Create focus change analysis
            if 'focus_analysis' in data_dict:
                fig4 = self.create_focus_change_impact_analysis(data_dict['focus_analysis'])
                created_files.append('focus_change_impact_analysis')
                plt.close(fig4)
            
            # Create executive dashboard
            if 'executive_summary' in data_dict:
                fig5 = self.create_executive_dashboard(data_dict['executive_summary'])
                created_files.append('executive_dashboard')
                plt.close(fig5)
        
        except Exception as e:
            print(f"Error creating visualizations: {str(e)}")
        
        return created_files