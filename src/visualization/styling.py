"""
Module: styling
Description: Professional styling configuration for QS WUR analysis visualizations
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- matplotlib
- seaborn
- pandas
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional

# Symbiosis branding colors
SYMBIOSIS_COLORS = {
    'primary': '#1f4e79',      # Deep Blue
    'secondary': '#f39c12',    # Orange
    'accent': '#27ae60',       # Green
    'neutral': '#7f8c8d',      # Gray
    'light': '#ecf0f1',        # Light Gray
    'white': '#ffffff',        # White
    'dark': '#2c3e50',         # Dark Blue
    'success': '#27ae60',      # Green
    'warning': '#f39c12',      # Orange
    'danger': '#e74c3c'        # Red
}

# Professional color palettes
COLOR_PALETTES = {
    'categorical': [
        SYMBIOSIS_COLORS['primary'],
        SYMBIOSIS_COLORS['secondary'],
        SYMBIOSIS_COLORS['accent'],
        SYMBIOSIS_COLORS['danger'],
        SYMBIOSIS_COLORS['neutral'],
        '#9b59b6',  # Purple
        '#34495e',  # Dark Gray
        '#16a085',  # Teal
        '#e67e22',  # Dark Orange
        '#95a5a6'   # Light Gray
    ],
    'sequential': sns.color_palette("Blues", 10),
    'diverging': sns.color_palette("RdBu_r", 10),
    'performance': [
        SYMBIOSIS_COLORS['success'],   # Top performers
        SYMBIOSIS_COLORS['accent'],    # Good performers
        SYMBIOSIS_COLORS['warning'],   # Average performers
        SYMBIOSIS_COLORS['danger']     # Poor performers
    ],
    'ranking_tiers': {
        'Top 100': SYMBIOSIS_COLORS['success'],
        'Top 200': SYMBIOSIS_COLORS['accent'],
        'Top 500': SYMBIOSIS_COLORS['secondary'],
        'Top 1000': SYMBIOSIS_COLORS['warning'],
        'Beyond 1000': SYMBIOSIS_COLORS['danger'],
        'Not Ranked': SYMBIOSIS_COLORS['neutral']
    }
}

def setup_visualization_style():
    """
    Set up global styling for all visualizations.
    
    Returns
    -------
    Dict
        Color palette configuration
    """
    # Set style
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Global matplotlib settings
    plt.rcParams.update({
        # Font settings
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica'],
        'font.size': 11,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 12,
        'axes.labelweight': 'normal',
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'legend.title_fontsize': 11,
        
        # Figure settings
        'figure.figsize': (12, 8),
        'figure.dpi': 100,
        'figure.facecolor': 'white',
        'figure.edgecolor': 'none',
        
        # Axes settings
        'axes.facecolor': 'white',
        'axes.edgecolor': SYMBIOSIS_COLORS['neutral'],
        'axes.linewidth': 0.8,
        'axes.grid': True,
        'axes.axisbelow': True,
        'axes.spines.left': True,
        'axes.spines.bottom': True,
        'axes.spines.top': False,
        'axes.spines.right': False,
        
        # Grid settings
        'grid.color': '#e0e0e0',
        'grid.alpha': 0.7,
        'grid.linewidth': 0.5,
        
        # Line settings
        'lines.linewidth': 2.0,
        'lines.markersize': 6,
        
        # Legend settings
        'legend.frameon': True,
        'legend.framealpha': 0.9,
        'legend.fancybox': True,
        'legend.shadow': False,
        'legend.edgecolor': SYMBIOSIS_COLORS['neutral'],
        
        # Saving settings
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.2,
        'savefig.facecolor': 'white',
        'savefig.edgecolor': 'none'
    })
    
    # Set seaborn palette
    sns.set_palette(COLOR_PALETTES['categorical'])
    
    return COLOR_PALETTES

def apply_symbiosis_branding(ax, title: str = None, subtitle: str = None, 
                           show_logo_placeholder: bool = False):
    """
    Apply Symbiosis branding to a matplotlib axes.
    
    Parameters
    ----------
    ax : matplotlib.axes.Axes
        The axes to apply branding to
    title : str, optional
        Main title for the plot
    subtitle : str, optional
        Subtitle for the plot
    show_logo_placeholder : bool, default=False
        Whether to show logo placeholder
    """
    if title:
        ax.set_title(title, fontsize=16, fontweight='bold', 
                    color=SYMBIOSIS_COLORS['primary'], pad=20)
    
    if subtitle:
        ax.text(0.5, 0.98, subtitle, transform=ax.transAxes, 
                fontsize=12, ha='center', va='top',
                color=SYMBIOSIS_COLORS['neutral'])
    
    # Add institutional attribution
    ax.text(0.99, 0.01, 'Symbiosis International (Deemed University)', 
            transform=ax.transAxes, fontsize=8, ha='right', va='bottom',
            color=SYMBIOSIS_COLORS['neutral'], alpha=0.7)
    
    if show_logo_placeholder:
        # Add logo placeholder
        logo_box = mpatches.Rectangle((0.01, 0.01), 0.08, 0.06, 
                                    transform=ax.transAxes,
                                    facecolor=SYMBIOSIS_COLORS['light'],
                                    edgecolor=SYMBIOSIS_COLORS['neutral'],
                                    linewidth=1)
        ax.add_patch(logo_box)
        ax.text(0.05, 0.04, 'LOGO', transform=ax.transAxes, 
                fontsize=8, ha='center', va='center',
                color=SYMBIOSIS_COLORS['neutral'])

def create_performance_colormap(ranks: List[float]) -> Dict:
    """
    Create a color mapping based on performance rankings.
    
    Parameters
    ----------
    ranks : List[float]
        List of ranking values
        
    Returns
    -------
    Dict
        Color mapping for rankings
    """
    color_map = {}
    
    for rank in ranks:
        if pd.isna(rank):
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Not Ranked']
        elif rank <= 100:
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Top 100']
        elif rank <= 200:
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Top 200']
        elif rank <= 500:
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Top 500']
        elif rank <= 1000:
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Top 1000']
        else:
            color_map[rank] = COLOR_PALETTES['ranking_tiers']['Beyond 1000']
    
    return color_map

def get_institution_color(institution_name: str, highlight_symbiosis: bool = True) -> str:
    """
    Get color for institution with special highlighting for Symbiosis.
    
    Parameters
    ----------
    institution_name : str
        Name of the institution
    highlight_symbiosis : bool, default=True
        Whether to highlight Symbiosis with special color
        
    Returns
    -------
    str
        Color code for the institution
    """
    if highlight_symbiosis and 'Symbiosis' in institution_name:
        return SYMBIOSIS_COLORS['primary']
    else:
        return SYMBIOSIS_COLORS['secondary']

def format_ranking_axis(ax, axis: str = 'y', reverse: bool = True):
    """
    Format ranking axis with proper scaling and labels.
    
    Parameters
    ----------
    ax : matplotlib.axes.Axes
        The axes to format
    axis : str, default='y'
        Which axis to format ('x' or 'y')
    reverse : bool, default=True
        Whether to reverse the axis (lower ranks at top)
    """
    if axis == 'y':
        if reverse:
            ax.invert_yaxis()
        ax.set_ylabel('QS World University Ranking', fontweight='bold')
        
        # Add rank tier lines
        for tier, rank in [(100, 'Top 100'), (200, 'Top 200'), (500, 'Top 500'), (1000, 'Top 1000')]:
            ax.axhline(y=tier, color=SYMBIOSIS_COLORS['neutral'], 
                      linestyle='--', alpha=0.3, linewidth=0.8)
            ax.text(ax.get_xlim()[1], tier, f'  {rank}', 
                   va='center', ha='left', fontsize=8, 
                   color=SYMBIOSIS_COLORS['neutral'], alpha=0.7)
    
    elif axis == 'x':
        if reverse:
            ax.invert_xaxis()
        ax.set_xlabel('QS World University Ranking', fontweight='bold')
        
        # Add rank tier lines
        for tier, rank in [(100, 'Top 100'), (200, 'Top 200'), (500, 'Top 500'), (1000, 'Top 1000')]:
            ax.axvline(x=tier, color=SYMBIOSIS_COLORS['neutral'], 
                      linestyle='--', alpha=0.3, linewidth=0.8)
            ax.text(tier, ax.get_ylim()[1], f'{rank}\n', 
                   ha='center', va='top', fontsize=8, 
                   color=SYMBIOSIS_COLORS['neutral'], alpha=0.7, rotation=45)

def add_trend_arrow(ax, x_pos: float, y_start: float, y_end: float, 
                   improvement: bool = True, label: str = None):
    """
    Add trend arrow to show improvement or decline.
    
    Parameters
    ----------
    ax : matplotlib.axes.Axes
        The axes to add arrow to
    x_pos : float
        X position for the arrow
    y_start : float
        Starting Y position
    y_end : float
        Ending Y position
    improvement : bool, default=True
        Whether the trend is improvement (green) or decline (red)
    label : str, optional
        Label for the trend
    """
    color = SYMBIOSIS_COLORS['success'] if improvement else SYMBIOSIS_COLORS['danger']
    
    ax.annotate('', xy=(x_pos, y_end), xytext=(x_pos, y_start),
                arrowprops=dict(arrowstyle='->', color=color, lw=2))
    
    if label:
        mid_y = (y_start + y_end) / 2
        ax.text(x_pos + 0.1, mid_y, label, va='center', ha='left',
                color=color, fontweight='bold', fontsize=9)

def create_legend_for_tiers(ax, position: str = 'upper right'):
    """
    Create a legend showing ranking tiers.
    
    Parameters
    ----------
    ax : matplotlib.axes.Axes
        The axes to add legend to
    position : str, default='upper right'
        Legend position
    """
    legend_elements = []
    
    for tier, color in COLOR_PALETTES['ranking_tiers'].items():
        if tier != 'Not Ranked':  # Skip not ranked for cleaner legend
            legend_elements.append(mpatches.Patch(color=color, label=tier))
    
    ax.legend(handles=legend_elements, loc=position, 
             title='Ranking Tiers', title_fontsize=11, fontsize=10)

def save_figure(fig, filename: str, output_dir: str = None, 
                formats: List[str] = ['png', 'pdf']):
    """
    Save figure in multiple formats with consistent settings.
    
    Parameters
    ----------
    fig : matplotlib.figure.Figure
        The figure to save
    filename : str
        Base filename (without extension)
    output_dir : str, optional
        Output directory (defaults to current directory)
    formats : List[str], default=['png', 'pdf']
        List of formats to save
    """
    import os
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    for fmt in formats:
        if output_dir:
            filepath = os.path.join(output_dir, f"{filename}.{fmt}")
        else:
            filepath = f"{filename}.{fmt}"
        
        fig.savefig(filepath, format=fmt, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')

def create_metric_comparison_colors(values: List[float], 
                                  comparison_value: float) -> List[str]:
    """
    Create colors for metric comparison (better/worse than comparison value).
    
    Parameters
    ----------
    values : List[float]
        Values to compare
    comparison_value : float
        Reference value for comparison
        
    Returns
    -------
    List[str]
        List of colors based on comparison
    """
    colors = []
    
    for value in values:
        if pd.isna(value) or pd.isna(comparison_value):
            colors.append(SYMBIOSIS_COLORS['neutral'])
        elif value > comparison_value:
            colors.append(SYMBIOSIS_COLORS['success'])
        elif value < comparison_value:
            colors.append(SYMBIOSIS_COLORS['danger'])
        else:
            colors.append(SYMBIOSIS_COLORS['neutral'])
    
    return colors

def apply_professional_formatting(ax, remove_spines: bool = True, 
                                grid_alpha: float = 0.3):
    """
    Apply professional formatting to axes.
    
    Parameters
    ----------
    ax : matplotlib.axes.Axes
        The axes to format
    remove_spines : bool, default=True
        Whether to remove top and right spines
    grid_alpha : float, default=0.3
        Grid transparency level
    """
    if remove_spines:
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_color(SYMBIOSIS_COLORS['neutral'])
        ax.spines['bottom'].set_color(SYMBIOSIS_COLORS['neutral'])
    
    ax.grid(True, alpha=grid_alpha, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Improve tick formatting
    ax.tick_params(colors=SYMBIOSIS_COLORS['dark'], which='both')

# Initialize styling when module is imported
COLORS = setup_visualization_style()