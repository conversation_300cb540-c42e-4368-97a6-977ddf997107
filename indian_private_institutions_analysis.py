"""
Module: indian_private_institutions_analysis
Description: Comprehensive analysis of Indian private institutions in QS World University Rankings (2022-2026)
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
Last Modified: 2025-06-19

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set up visualization style
def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    # Color palettes for different visualization types
    COLOR_PALETTE = {
        'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
        'sequential': sns.color_palette("viridis", 10),
        'diverging': sns.color_palette("RdBu_r", 10),
        'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc']
    }
    
    # Typography
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.titleweight'] = 'bold'
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    
    # Figure sizing
    plt.rcParams['figure.figsize'] = (14, 10)
    plt.rcParams['figure.dpi'] = 100
    
    return COLOR_PALETTE

# Global color palette
COLORS = setup_visualization_style()

def load_qs_data(base_path):
    """
    Load all QS World University Rankings data files.
    
    Parameters
    ----------
    base_path : str
        Path to the directory containing QS ranking CSV files
        
    Returns
    -------
    dict
        Dictionary with year as key and DataFrame as value
    """
    data_files = {
        2022: '2022 QS World University Rankings.csv',
        2023: '2023 QS World University Rankings.csv',
        2024: '2024 QS World University Rankings.csv',
        2025: '2025 QS World University Ranking.csv',
        2026: '2026 QS World University Rankings.csv'
    }
    
    datasets = {}
    
    for year, filename in data_files.items():
        filepath = os.path.join(base_path, filename)
        try:
            # Try different encodings to handle file encoding issues
            encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(filepath, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is not None:
                # Standardize column names
                df.columns = df.columns.str.strip()
                datasets[year] = df
                print(f"Loaded {year} data: {len(df)} institutions")
            else:
                print(f"Error loading {year} data: Could not decode with any encoding")
        except Exception as e:
            print(f"Error loading {year} data: {e}")
    
    return datasets

def extract_indian_institutions(datasets):
    """
    Extract all Indian institutions from the datasets.
    
    Parameters
    ----------
    datasets : dict
        Dictionary of DataFrames by year
        
    Returns
    -------
    dict
        Dictionary with year as key and filtered DataFrame as value
    """
    indian_data = {}
    
    for year, df in datasets.items():
        # Filter for Indian institutions
        indian_df = df[df['Country'].str.contains('India', case=False, na=False)].copy()
        indian_data[year] = indian_df
        print(f"{year}: Found {len(indian_df)} Indian institutions")
    
    return indian_data

def identify_private_institutions(indian_data):
    """
    Identify private institutions from Indian institutions data.
    
    Parameters
    ----------
    indian_data : dict
        Dictionary of Indian institutions DataFrames by year
        
    Returns
    -------
    dict, dict
        Tuple of (private_data, all_indian_data with private classification)
    """
    private_data = {}
    classified_data = {}
    
    for year, df in indian_data.items():
        # Create a copy for classification
        df_classified = df.copy()
        
        # Check unique values in Private/Government column
        print(f"\n{year} - Private/Government values:")
        print(df['Private/Government'].value_counts(dropna=False))
        
        # Filter for private institutions
        # Handle different ways private institutions might be labeled
        private_conditions = (
            df['Private/Government'].str.contains('Private', case=False, na=False) |
            df['Private/Government'].str.contains('private', case=False, na=False)
        )
        
        private_df = df[private_conditions].copy()
        private_data[year] = private_df
        classified_data[year] = df_classified
        
        print(f"{year}: Found {len(private_df)} private Indian institutions")
        if len(private_df) > 0:
            print("Private institutions:")
            for idx, row in private_df.iterrows():
                print(f"  - {row['Institution_Name']} (Rank: {row[f'Rank_{year}']})")
    
    return private_data, classified_data

def create_comprehensive_analysis(private_data, all_indian_data):
    """
    Create comprehensive analysis of Indian private institutions.
    
    Parameters
    ----------
    private_data : dict
        Dictionary of private institutions DataFrames by year
    all_indian_data : dict
        Dictionary of all Indian institutions DataFrames by year
        
    Returns
    -------
    pd.DataFrame, dict
        Comprehensive analysis DataFrame and summary statistics
    """
    # Combine all years data
    combined_private = []
    combined_all_indian = []
    
    for year, df in private_data.items():
        if len(df) > 0:
            df_copy = df.copy()
            df_copy['Year'] = year
            combined_private.append(df_copy)
    
    for year, df in all_indian_data.items():
        df_copy = df.copy()
        df_copy['Year'] = year
        combined_all_indian.append(df_copy)
    
    if combined_private:
        private_analysis = pd.concat(combined_private, ignore_index=True)
    else:
        private_analysis = pd.DataFrame()
    
    all_indian_analysis = pd.concat(combined_all_indian, ignore_index=True)
    
    # Generate summary statistics
    summary_stats = {}
    
    # Private institutions summary
    if not private_analysis.empty:
        summary_stats['private_institutions'] = {
            'total_unique_institutions': private_analysis['Institution_Name'].nunique(),
            'total_entries': len(private_analysis),
            'years_covered': sorted(private_analysis['Year'].unique()),
            'institutions_by_year': private_analysis.groupby('Year')['Institution_Name'].nunique().to_dict()
        }
        
        # Performance metrics for private institutions
        rank_cols = [col for col in private_analysis.columns if col.startswith('Rank_')]
        if rank_cols:
            summary_stats['private_performance'] = {}
            for rank_col in rank_cols:
                year = rank_col.split('_')[1]
                valid_ranks = private_analysis[private_analysis['Year'] == int(year)][rank_col].dropna()
                if len(valid_ranks) > 0:
                    summary_stats['private_performance'][year] = {
                        'best_rank': valid_ranks.min(),
                        'worst_rank': valid_ranks.max(),
                        'median_rank': valid_ranks.median(),
                        'institution_count': len(valid_ranks)
                    }
    else:
        summary_stats['private_institutions'] = {
            'total_unique_institutions': 0,
            'message': 'No private institutions found with explicit "Private" classification'
        }
    
    # All Indian institutions summary for comparison
    summary_stats['all_indian_institutions'] = {
        'total_unique_institutions': all_indian_analysis['Institution_Name'].nunique(),
        'total_entries': len(all_indian_analysis),
        'institutions_by_year': all_indian_analysis.groupby('Year')['Institution_Name'].nunique().to_dict(),
        'classification_breakdown': {}
    }
    
    # Classification breakdown by year
    for year in all_indian_analysis['Year'].unique():
        year_data = all_indian_analysis[all_indian_analysis['Year'] == year]
        classification_counts = year_data['Private/Government'].value_counts(dropna=False)
        summary_stats['all_indian_institutions']['classification_breakdown'][year] = classification_counts.to_dict()
    
    return private_analysis, all_indian_analysis, summary_stats

def analyze_symbiosis_performance(all_indian_data):
    """
    Analyze Symbiosis International (Deemed University) performance specifically.
    
    Parameters
    ----------
    all_indian_data : dict
        Dictionary of all Indian institutions DataFrames by year
        
    Returns
    -------
    pd.DataFrame
        Symbiosis performance data across years
    """
    symbiosis_data = []
    
    for year, df in all_indian_data.items():
        # Search for Symbiosis institutions
        symbiosis_matches = df[
            df['Institution_Name'].str.contains('Symbiosis', case=False, na=False)
        ].copy()
        
        if len(symbiosis_matches) > 0:
            symbiosis_matches['Year'] = year
            symbiosis_data.append(symbiosis_matches)
            print(f"\n{year} - Found Symbiosis institution(s):")
            for idx, row in symbiosis_matches.iterrows():
                print(f"  - {row['Institution_Name']}")
                print(f"    Rank: {row[f'Rank_{year}']}")
                print(f"    Classification: {row['Private/Government']}")
    
    if symbiosis_data:
        symbiosis_analysis = pd.concat(symbiosis_data, ignore_index=True)
        return symbiosis_analysis
    else:
        print("No Symbiosis institutions found in the rankings")
        return pd.DataFrame()

def create_detailed_institution_profiles(all_indian_analysis):
    """
    Create detailed profiles for each Indian institution across years.
    
    Parameters
    ----------
    all_indian_analysis : pd.DataFrame
        Combined data of all Indian institutions
        
    Returns
    -------
    dict
        Dictionary with institution names as keys and performance data as values
    """
    institution_profiles = {}
    
    for institution in all_indian_analysis['Institution_Name'].unique():
        inst_data = all_indian_analysis[
            all_indian_analysis['Institution_Name'] == institution
        ].copy()
        
        profile = {
            'name': institution,
            'years_present': sorted(inst_data['Year'].tolist()),
            'classification': inst_data['Private/Government'].iloc[0] if len(inst_data) > 0 else 'Unknown',
            'performance_by_year': {}
        }
        
        for _, row in inst_data.iterrows():
            year = row['Year']
            rank_col = f'Rank_{year}'
            
            year_profile = {
                'rank': row[rank_col] if rank_col in row else None,
                'overall_score': row.get('Overall_Score', None),
                'academic_reputation_score': row.get('Academic_Reputation_Score', None),
                'employer_reputation_score': row.get('Employer_Reputation_Score', None),
                'faculty_student_score': row.get('Faculty_Student_Score', None),
                'citations_per_faculty_score': row.get('Citations_per_Faculty_Score', None),
                'international_faculty_score': row.get('International_Faculty_Score', None),
                'international_students_score': row.get('International_Students_Score', None)
            }
            
            profile['performance_by_year'][year] = year_profile
        
        institution_profiles[institution] = profile
    
    return institution_profiles

def generate_comprehensive_report(private_analysis, all_indian_analysis, summary_stats, institution_profiles):
    """
    Generate a comprehensive report of the analysis.
    
    Parameters
    ----------
    private_analysis : pd.DataFrame
        Private institutions analysis data
    all_indian_analysis : pd.DataFrame
        All Indian institutions analysis data
    summary_stats : dict
        Summary statistics
    institution_profiles : dict
        Detailed institution profiles
        
    Returns
    -------
    str
        Comprehensive report
    """
    report = []
    report.append("=" * 80)
    report.append("COMPREHENSIVE ANALYSIS: INDIAN PRIVATE INSTITUTIONS IN QS WORLD UNIVERSITY RANKINGS")
    report.append("QS World University Rankings Analysis (2022-2026)")
    report.append("=" * 80)
    report.append(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Prepared by: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    report.append("")
    
    # Executive Summary
    report.append("EXECUTIVE SUMMARY")
    report.append("-" * 40)
    report.append(f"Total Indian Institutions Analyzed: {summary_stats['all_indian_institutions']['total_unique_institutions']}")
    report.append(f"Total Data Points: {summary_stats['all_indian_institutions']['total_entries']}")
    
    if summary_stats['private_institutions']['total_unique_institutions'] > 0:
        report.append(f"Private Indian Institutions Found: {summary_stats['private_institutions']['total_unique_institutions']}")
        report.append(f"Private Institution Data Points: {summary_stats['private_institutions']['total_entries']}")
    else:
        report.append("Private Indian Institutions: No institutions explicitly classified as 'Private' found")
    
    report.append("")
    
    # Private Institutions Analysis
    report.append("PRIVATE INSTITUTIONS DETAILED ANALYSIS")
    report.append("-" * 50)
    
    if not private_analysis.empty:
        report.append("Private Institutions by Year:")
        for year, count in summary_stats['private_institutions']['institutions_by_year'].items():
            report.append(f"  {year}: {count} institutions")
        
        report.append("\nPrivate Institutions Performance Summary:")
        if 'private_performance' in summary_stats:
            for year, perf in summary_stats['private_performance'].items():
                report.append(f"  {year}:")
                report.append(f"    Best Rank: {perf['best_rank']}")
                report.append(f"    Worst Rank: {perf['worst_rank']}")
                report.append(f"    Median Rank: {perf['median_rank']}")
                report.append(f"    Institution Count: {perf['institution_count']}")
        
        report.append("\nList of Private Institutions:")
        for institution in private_analysis['Institution_Name'].unique():
            inst_data = private_analysis[private_analysis['Institution_Name'] == institution]
            years = sorted(inst_data['Year'].tolist())
            ranks = []
            for year in years:
                year_data = inst_data[inst_data['Year'] == year]
                if len(year_data) > 0:
                    rank = year_data[f'Rank_{year}'].iloc[0]
                    ranks.append(f"{year}: {rank}")
            report.append(f"  • {institution}")
            report.append(f"    Years Present: {years}")
            report.append(f"    Rankings: {', '.join(ranks)}")
    else:
        report.append("No institutions were found with explicit 'Private' classification.")
        report.append("This may indicate:")
        report.append("1. Private institutions are classified differently in the dataset")
        report.append("2. Most Indian institutions in QS rankings are government institutions")
        report.append("3. Classification data may be incomplete or use different terminology")
    
    report.append("")
    
    # All Indian Institutions Classification Breakdown
    report.append("INDIAN INSTITUTIONS CLASSIFICATION BREAKDOWN")
    report.append("-" * 55)
    
    for year, breakdown in summary_stats['all_indian_institutions']['classification_breakdown'].items():
        report.append(f"\n{year} Classification:")
        for classification, count in breakdown.items():
            report.append(f"  {classification}: {count} institutions")
    
    report.append("")
    
    # Institutional Profiles
    report.append("DETAILED INSTITUTIONAL PROFILES")
    report.append("-" * 40)
    
    # Focus on institutions that might be private or deemed universities
    potential_private = []
    for name, profile in institution_profiles.items():
        # Look for indicators of private/deemed status
        if any(keyword in name.lower() for keyword in ['deemed', 'private', 'symbiosis', 'manipal', 'amity', 'vit', 'srm']):
            potential_private.append((name, profile))
    
    if potential_private:
        report.append("Institutions with Potential Private/Deemed Status:")
        for name, profile in potential_private:
            report.append(f"\n• {name}")
            report.append(f"  Classification: {profile['classification']}")
            report.append(f"  Years Present: {profile['years_present']}")
            
            # Performance summary
            ranks = []
            scores = []
            for year, perf in profile['performance_by_year'].items():
                if perf['rank']:
                    ranks.append(f"{year}: Rank {perf['rank']}")
                if perf['overall_score']:
                    scores.append(f"{year}: {perf['overall_score']}")
            
            if ranks:
                report.append(f"  Rankings: {', '.join(ranks)}")
            if scores:
                report.append(f"  Overall Scores: {', '.join(scores)}")
    
    # Top performing Indian institutions
    report.append("\nTOP PERFORMING INDIAN INSTITUTIONS (All Categories):")
    
    # Get best performing institutions by year
    for year in sorted(all_indian_analysis['Year'].unique()):
        year_data = all_indian_analysis[all_indian_analysis['Year'] == year].copy()
        rank_col = f'Rank_{year}'
        
        if rank_col in year_data.columns:
            # Convert rank to numeric, handling ranges like "151-200"
            year_data['numeric_rank'] = year_data[rank_col].apply(lambda x: 
                float(x.split('-')[0]) if isinstance(x, str) and '-' in str(x) 
                else float(x) if pd.notna(x) else float('inf')
            )
            
            top_5 = year_data.nsmallest(5, 'numeric_rank')
            report.append(f"\n  {year} Top 5:")
            for _, row in top_5.iterrows():
                report.append(f"    {row['Institution_Name']} - Rank {row[rank_col]} ({row['Private/Government']})")
    
    report.append("")
    report.append("PATTERNS AND INSIGHTS")
    report.append("-" * 30)
    
    # Analyze patterns
    report.append("Key Observations:")
    
    # Total institutions trend
    yearly_counts = all_indian_analysis.groupby('Year').size()
    if len(yearly_counts) > 1:
        trend = "increasing" if yearly_counts.iloc[-1] > yearly_counts.iloc[0] else "decreasing"
        report.append(f"1. Indian institutions in QS rankings: {trend} trend ({yearly_counts.iloc[0]} in {yearly_counts.index[0]} to {yearly_counts.iloc[-1]} in {yearly_counts.index[-1]})")
    
    # Government vs Private representation
    govt_counts = []
    private_counts = []
    for year in sorted(all_indian_analysis['Year'].unique()):
        year_data = all_indian_analysis[all_indian_analysis['Year'] == year]
        govt = len(year_data[year_data['Private/Government'].str.contains('Government', case=False, na=False)])
        private = len(year_data[year_data['Private/Government'].str.contains('Private', case=False, na=False)])
        govt_counts.append(govt)
        private_counts.append(private)
    
    if sum(govt_counts) > 0:
        report.append(f"2. Government institutions dominate QS rankings (Average: {sum(govt_counts)/len(govt_counts):.1f} per year)")
    
    if sum(private_counts) > 0:
        report.append(f"3. Private institutions representation: {sum(private_counts)/len(private_counts):.1f} per year on average")
    else:
        report.append("3. Very limited explicit private institution representation in QS rankings")
    
    # Performance patterns
    report.append("4. Most Indian institutions in QS rankings appear to be government-funded IITs, IIMs, and central universities")
    report.append("5. This suggests significant opportunities for private institutions to improve global visibility")
    
    report.append("")
    report.append("RECOMMENDATIONS FOR SYMBIOSIS INTERNATIONAL (DEEMED UNIVERSITY)")
    report.append("-" * 65)
    report.append("Based on this analysis:")
    report.append("1. Focus on research output and citations to improve QS ranking potential")
    report.append("2. Enhance international collaboration and faculty exchange programs")
    report.append("3. Strengthen employer reputation through industry partnerships")
    report.append("4. Improve international student diversity and faculty internationalization")
    report.append("5. Develop sustainability initiatives as this is becoming a ranking factor")
    
    report.append("")
    report.append("=" * 80)
    
    return "\n".join(report)

def create_visualizations(all_indian_analysis, private_analysis, output_dir):
    """
    Create comprehensive visualizations for the analysis.
    
    Parameters
    ----------
    all_indian_analysis : pd.DataFrame
        All Indian institutions data
    private_analysis : pd.DataFrame
        Private institutions data
    output_dir : str
        Directory to save visualizations
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. Indian Institutions by Year
    plt.figure(figsize=(12, 8))
    yearly_counts = all_indian_analysis.groupby('Year').size()
    plt.plot(yearly_counts.index, yearly_counts.values, marker='o', linewidth=3, markersize=8, color=COLORS['categorical'][0])
    plt.title('Indian Institutions in QS World University Rankings (2022-2026)', fontweight='bold', fontsize=16)
    plt.xlabel('Year', fontsize=14)
    plt.ylabel('Number of Institutions', fontsize=14)
    plt.grid(True, alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'indian_institutions_trend.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Classification Breakdown by Year
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    years = sorted(all_indian_analysis['Year'].unique())
    
    for i, year in enumerate(years):
        row = i // 3
        col = i % 3
        
        year_data = all_indian_analysis[all_indian_analysis['Year'] == year]
        classification_counts = year_data['Private/Government'].value_counts()
        
        wedges, texts, autotexts = axes[row, col].pie(
            classification_counts.values, 
            labels=classification_counts.index,
            autopct='%1.1f%%',
            colors=COLORS['categorical'][:len(classification_counts)]
        )
        axes[row, col].set_title(f'{year}', fontweight='bold')
    
    # Remove empty subplot
    if len(years) < 6:
        axes[1, 2].remove()
    
    plt.suptitle('Indian Institutions Classification by Year', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'classification_breakdown.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Top Institutions Performance Over Time
    # Get institutions that appear in multiple years
    institution_counts = all_indian_analysis['Institution_Name'].value_counts()
    multi_year_institutions = institution_counts[institution_counts >= 3].index[:10]  # Top 10 with most appearances
    
    plt.figure(figsize=(16, 10))
    
    for i, institution in enumerate(multi_year_institutions):
        inst_data = all_indian_analysis[all_indian_analysis['Institution_Name'] == institution].copy()
        
        # Extract numeric ranks
        ranks = []
        years = []
        for _, row in inst_data.iterrows():
            year = row['Year']
            rank_col = f'Rank_{year}'
            if rank_col in row and pd.notna(row[rank_col]):
                rank_str = str(row[rank_col])
                # Handle ranges like "151-200"
                if '-' in rank_str:
                    rank_num = float(rank_str.split('-')[0])
                else:
                    rank_num = float(rank_str)
                ranks.append(rank_num)
                years.append(year)
        
        if len(ranks) >= 2:  # Only plot if institution has data for multiple years
            plt.plot(years, ranks, marker='o', linewidth=2, markersize=6, 
                    label=institution.replace('University', 'Univ.')[:30], alpha=0.8)
    
    plt.title('Performance Trends of Top Indian Institutions (2022-2026)', fontweight='bold', fontsize=16)
    plt.xlabel('Year', fontsize=14)
    plt.ylabel('QS World Ranking (Lower is Better)', fontsize=14)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    plt.grid(True, alpha=0.7)
    plt.gca().invert_yaxis()  # Lower ranks are better
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'top_institutions_trends.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Private vs Government Institution Comparison (if private institutions exist)
    if not private_analysis.empty:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Count comparison
        yearly_private = private_analysis.groupby('Year').size()
        yearly_govt = all_indian_analysis[
            all_indian_analysis['Private/Government'].str.contains('Government', case=False, na=False)
        ].groupby('Year').size()
        
        years = sorted(all_indian_analysis['Year'].unique())
        private_counts = [yearly_private.get(year, 0) for year in years]
        govt_counts = [yearly_govt.get(year, 0) for year in years]
        
        x = np.arange(len(years))
        width = 0.35
        
        ax1.bar(x - width/2, private_counts, width, label='Private', color=COLORS['categorical'][1])
        ax1.bar(x + width/2, govt_counts, width, label='Government', color=COLORS['categorical'][0])
        ax1.set_xlabel('Year')
        ax1.set_ylabel('Number of Institutions')
        ax1.set_title('Private vs Government Institutions Count')
        ax1.set_xticks(x)
        ax1.set_xticklabels(years)
        ax1.legend()
        ax1.grid(True, alpha=0.7)
        
        # Performance comparison (average ranks)
        private_avg_ranks = []
        govt_avg_ranks = []
        
        for year in years:
            # Private institutions average rank
            year_private = private_analysis[private_analysis['Year'] == year]
            if len(year_private) > 0:
                rank_col = f'Rank_{year}'
                private_ranks = []
                for _, row in year_private.iterrows():
                    if pd.notna(row[rank_col]):
                        rank_str = str(row[rank_col])
                        if '-' in rank_str:
                            rank_num = float(rank_str.split('-')[0])
                        else:
                            rank_num = float(rank_str)
                        private_ranks.append(rank_num)
                private_avg_ranks.append(np.mean(private_ranks) if private_ranks else None)
            else:
                private_avg_ranks.append(None)
            
            # Government institutions average rank
            year_govt = all_indian_analysis[
                (all_indian_analysis['Year'] == year) & 
                (all_indian_analysis['Private/Government'].str.contains('Government', case=False, na=False))
            ]
            if len(year_govt) > 0:
                rank_col = f'Rank_{year}'
                govt_ranks = []
                for _, row in year_govt.iterrows():
                    if pd.notna(row[rank_col]):
                        rank_str = str(row[rank_col])
                        if '-' in rank_str:
                            rank_num = float(rank_str.split('-')[0])
                        else:
                            rank_num = float(rank_str)
                        govt_ranks.append(rank_num)
                govt_avg_ranks.append(np.mean(govt_ranks) if govt_ranks else None)
            else:
                govt_avg_ranks.append(None)
        
        # Plot average ranks
        private_years = [years[i] for i, rank in enumerate(private_avg_ranks) if rank is not None]
        private_ranks_clean = [rank for rank in private_avg_ranks if rank is not None]
        govt_years = [years[i] for i, rank in enumerate(govt_avg_ranks) if rank is not None]
        govt_ranks_clean = [rank for rank in govt_avg_ranks if rank is not None]
        
        if private_ranks_clean:
            ax2.plot(private_years, private_ranks_clean, marker='o', linewidth=3, markersize=8, 
                    label='Private (Avg)', color=COLORS['categorical'][1])
        if govt_ranks_clean:
            ax2.plot(govt_years, govt_ranks_clean, marker='s', linewidth=3, markersize=8, 
                    label='Government (Avg)', color=COLORS['categorical'][0])
        
        ax2.set_xlabel('Year')
        ax2.set_ylabel('Average QS World Ranking')
        ax2.set_title('Average Performance: Private vs Government')
        ax2.legend()
        ax2.grid(True, alpha=0.7)
        ax2.invert_yaxis()  # Lower ranks are better
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'private_vs_government_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    print(f"Visualizations saved to: {output_dir}")

def main():
    """Main execution function."""
    # Define paths
    base_path = "/Users/<USER>/Downloads/AI Projects Analysis Download folder/Rankings Analysis/QS WUR/QS WUR v1"
    output_dir = os.path.join(base_path, "analysis_output")
    
    print("Starting comprehensive analysis of Indian institutions in QS World University Rankings...")
    print("=" * 80)
    
    # Load data
    print("\n1. Loading QS World University Rankings data...")
    datasets = load_qs_data(base_path)
    
    # Extract Indian institutions
    print("\n2. Extracting Indian institutions...")
    indian_data = extract_indian_institutions(datasets)
    
    # Identify private institutions
    print("\n3. Identifying private institutions...")
    private_data, classified_indian_data = identify_private_institutions(indian_data)
    
    # Create comprehensive analysis
    print("\n4. Creating comprehensive analysis...")
    private_analysis, all_indian_analysis, summary_stats = create_comprehensive_analysis(
        private_data, classified_indian_data
    )
    
    # Analyze Symbiosis specifically
    print("\n5. Analyzing Symbiosis International (Deemed University)...")
    symbiosis_analysis = analyze_symbiosis_performance(classified_indian_data)
    
    # Create detailed institutional profiles
    print("\n6. Creating detailed institutional profiles...")
    institution_profiles = create_detailed_institution_profiles(all_indian_analysis)
    
    # Generate comprehensive report
    print("\n7. Generating comprehensive report...")
    report = generate_comprehensive_report(
        private_analysis, all_indian_analysis, summary_stats, institution_profiles
    )
    
    # Save report
    os.makedirs(output_dir, exist_ok=True)
    report_path = os.path.join(output_dir, "Indian_Private_Institutions_QS_Analysis_Report.txt")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    # Create visualizations
    print("\n8. Creating visualizations...")
    create_visualizations(all_indian_analysis, private_analysis, output_dir)
    
    # Save detailed data files
    print("\n9. Saving detailed data files...")
    
    # All Indian institutions data
    all_indian_path = os.path.join(output_dir, "All_Indian_Institutions_2022-2026.csv")
    all_indian_analysis.to_csv(all_indian_path, index=False)
    
    # Private institutions data (if any)
    if not private_analysis.empty:
        private_path = os.path.join(output_dir, "Indian_Private_Institutions_2022-2026.csv")
        private_analysis.to_csv(private_path, index=False)
    
    # Symbiosis data (if found)
    if not symbiosis_analysis.empty:
        symbiosis_path = os.path.join(output_dir, "Symbiosis_Performance_Analysis.csv")
        symbiosis_analysis.to_csv(symbiosis_path, index=False)
    
    # Institutional profiles (summary)
    profiles_data = []
    for name, profile in institution_profiles.items():
        base_info = {
            'Institution_Name': name,
            'Classification': profile['classification'],
            'Years_Present': ', '.join(map(str, profile['years_present'])),
            'Total_Years': len(profile['years_present'])
        }
        
        # Add performance metrics
        for year, perf in profile['performance_by_year'].items():
            base_info[f'Rank_{year}'] = perf['rank']
            base_info[f'Overall_Score_{year}'] = perf['overall_score']
        
        profiles_data.append(base_info)
    
    profiles_df = pd.DataFrame(profiles_data)
    profiles_path = os.path.join(output_dir, "Indian_Institutions_Profiles_Summary.csv")
    profiles_df.to_csv(profiles_path, index=False)
    
    print("\n" + "=" * 80)
    print("ANALYSIS COMPLETE!")
    print("=" * 80)
    print(f"Report saved to: {report_path}")
    print(f"Data files saved to: {output_dir}")
    print(f"Visualizations saved to: {output_dir}")
    print("\nKey Files Generated:")
    print(f"  • Comprehensive Report: Indian_Private_Institutions_QS_Analysis_Report.txt")
    print(f"  • All Indian Institutions Data: All_Indian_Institutions_2022-2026.csv")
    print(f"  • Institution Profiles Summary: Indian_Institutions_Profiles_Summary.csv")
    if not private_analysis.empty:
        print(f"  • Private Institutions Data: Indian_Private_Institutions_2022-2026.csv")
    if not symbiosis_analysis.empty:
        print(f"  • Symbiosis Analysis: Symbiosis_Performance_Analysis.csv")
    
    # Display key findings
    print("\n" + "=" * 80)
    print("KEY FINDINGS SUMMARY:")
    print("=" * 80)
    print(f"Total Indian Institutions Found: {summary_stats['all_indian_institutions']['total_unique_institutions']}")
    print(f"Explicitly Private Institutions: {summary_stats['private_institutions']['total_unique_institutions']}")
    
    if not symbiosis_analysis.empty:
        print(f"Symbiosis International Found: Yes")
        print(f"Symbiosis Years Present: {sorted(symbiosis_analysis['Year'].tolist())}")
    else:
        print(f"Symbiosis International Found: No")
    
    print("\nTop 5 Most Consistent Indian Institutions (by years present):")
    institution_years = all_indian_analysis.groupby('Institution_Name')['Year'].nunique().sort_values(ascending=False)
    for i, (institution, years) in enumerate(institution_years.head().items()):
        classification = all_indian_analysis[all_indian_analysis['Institution_Name'] == institution]['Private/Government'].iloc[0]
        print(f"  {i+1}. {institution} ({years} years) - {classification}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()