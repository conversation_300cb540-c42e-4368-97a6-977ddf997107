#!/usr/bin/env python3
"""
PDF Reader for QS WUR Analysis Project
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Purpose: Extract and analyze content from QS WUR 2026 Analysis draft 1.pdf
"""

import pdfplumber
import PyPDF2
import sys
import os
import fitz  # PyMuPDF

def read_pdf_with_pymupdf(pdf_path):
    """
    Read PDF content using PyMuPDF for better text extraction.

    Parameters
    ----------
    pdf_path : str
        Path to the PDF file

    Returns
    -------
    dict
        Dictionary containing extracted text, metadata, and structure
    """
    try:
        doc = fitz.open(pdf_path)
        content = {
            'metadata': doc.metadata,
            'num_pages': len(doc),
            'pages': [],
            'full_text': '',
            'slides': []
        }

        for page_num in range(len(doc)):
            page = doc[page_num]
            page_text = page.get_text()

            if page_text:
                content['pages'].append({
                    'page_num': page_num + 1,
                    'text': page_text,
                    'blocks': page.get_text("dict")["blocks"] if hasattr(page, 'get_text') else []
                })
                content['full_text'] += f"\n--- Page {page_num + 1} ---\n{page_text}\n"

                # Try to identify slide titles and content
                lines = page_text.split('\n')
                slide_content = {
                    'page_num': page_num + 1,
                    'title': '',
                    'content': [],
                    'bullet_points': []
                }

                for line in lines:
                    line = line.strip()
                    if line and len(line) > 2:  # Filter out very short lines
                        # Potential slide title (usually first significant line)
                        if not slide_content['title'] and len(line) > 5 and not line.startswith('•'):
                            slide_content['title'] = line
                        elif line.startswith('•') or line.startswith('-') or line.startswith('*') or line.startswith('→'):
                            slide_content['bullet_points'].append(line)
                        else:
                            slide_content['content'].append(line)

                content['slides'].append(slide_content)

        doc.close()
        return content

    except Exception as e:
        print(f"Error reading PDF with PyMuPDF: {str(e)}")
        return None

def read_pdf_with_pdfplumber(pdf_path):
    """
    Read PDF content using pdfplumber for better text extraction.
    
    Parameters
    ----------
    pdf_path : str
        Path to the PDF file
        
    Returns
    -------
    dict
        Dictionary containing extracted text, metadata, and structure
    """
    try:
        with pdfplumber.open(pdf_path) as pdf:
            content = {
                'metadata': pdf.metadata,
                'num_pages': len(pdf.pages),
                'pages': [],
                'full_text': '',
                'slides': []
            }
            
            for i, page in enumerate(pdf.pages):
                page_text = page.extract_text()
                if page_text:
                    content['pages'].append({
                        'page_num': i + 1,
                        'text': page_text,
                        'tables': page.extract_tables() if hasattr(page, 'extract_tables') else []
                    })
                    content['full_text'] += f"\n--- Page {i + 1} ---\n{page_text}\n"
                    
                    # Try to identify slide titles and content
                    lines = page_text.split('\n')
                    slide_content = {
                        'page_num': i + 1,
                        'title': '',
                        'content': [],
                        'bullet_points': []
                    }
                    
                    for line in lines:
                        line = line.strip()
                        if line:
                            # Potential slide title (usually first significant line)
                            if not slide_content['title'] and len(line) > 5:
                                slide_content['title'] = line
                            elif line.startswith('•') or line.startswith('-') or line.startswith('*'):
                                slide_content['bullet_points'].append(line)
                            else:
                                slide_content['content'].append(line)
                    
                    content['slides'].append(slide_content)
            
            return content
            
    except Exception as e:
        print(f"Error reading PDF with pdfplumber: {str(e)}")
        return None

def analyze_presentation_structure(content):
    """
    Analyze the presentation structure to identify key sections.
    
    Parameters
    ----------
    content : dict
        PDF content extracted by read_pdf_with_pdfplumber
        
    Returns
    -------
    dict
        Analysis of presentation structure
    """
    analysis = {
        'agenda_slides': [],
        'introduction_slides': [],
        'methodology_slides': [],
        'analysis_slides': [],
        'conclusion_slides': [],
        'recommendation_slides': [],
        'key_findings': [],
        'data_insights': []
    }
    
    # Keywords to identify different sections
    keywords = {
        'agenda': ['agenda', 'outline', 'overview', 'contents'],
        'introduction': ['introduction', 'background', 'context', 'overview'],
        'methodology': ['methodology', 'method', 'approach', 'analysis method'],
        'conclusion': ['conclusion', 'summary', 'findings', 'results'],
        'recommendation': ['recommendation', 'suggestions', 'action', 'next steps']
    }
    
    for slide in content['slides']:
        title_lower = slide['title'].lower()
        content_text = ' '.join(slide['content']).lower()
        
        # Categorize slides based on keywords
        for category, category_keywords in keywords.items():
            if any(keyword in title_lower or keyword in content_text for keyword in category_keywords):
                analysis[f'{category}_slides'].append(slide)
        
        # Extract key findings and insights
        for bullet in slide['bullet_points']:
            if any(word in bullet.lower() for word in ['increase', 'decrease', 'trend', 'performance', 'ranking']):
                analysis['key_findings'].append(bullet)
            if any(word in bullet.lower() for word in ['data', 'analysis', 'metric', 'score']):
                analysis['data_insights'].append(bullet)
    
    return analysis

def main():
    """Main function to read and analyze the PDF."""
    pdf_path = "QS WUR 2026 Analysis draft 1.pdf"

    if not os.path.exists(pdf_path):
        print(f"Error: PDF file '{pdf_path}' not found in current directory.")
        return

    print("Reading PDF content...")
    # Try PyMuPDF first, then fallback to pdfplumber
    content = read_pdf_with_pymupdf(pdf_path)
    if content is None:
        print("PyMuPDF failed, trying pdfplumber...")
        content = read_pdf_with_pdfplumber(pdf_path)
    
    if content is None:
        print("Failed to read PDF content.")
        return
    
    print(f"Successfully read PDF with {content['num_pages']} pages.")
    
    # Analyze presentation structure
    print("\nAnalyzing presentation structure...")
    analysis = analyze_presentation_structure(content)
    
    # Save extracted content to text file for review
    with open('pdf_content_analysis.txt', 'w', encoding='utf-8') as f:
        f.write("QS WUR 2026 Analysis - PDF Content Analysis\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"Document Metadata:\n")
        f.write(f"Number of pages: {content['num_pages']}\n")
        if content['metadata']:
            for key, value in content['metadata'].items():
                f.write(f"{key}: {value}\n")
        f.write("\n" + "=" * 50 + "\n\n")
        
        f.write("FULL DOCUMENT CONTENT:\n")
        f.write(content['full_text'])
        
        f.write("\n\n" + "=" * 50 + "\n")
        f.write("PRESENTATION STRUCTURE ANALYSIS:\n")
        f.write("=" * 50 + "\n\n")
        
        for category in ['agenda', 'introduction', 'methodology', 'conclusion', 'recommendation']:
            slides = analysis[f'{category}_slides']
            f.write(f"{category.upper()} SLIDES ({len(slides)} found):\n")
            f.write("-" * 30 + "\n")
            for slide in slides:
                f.write(f"Page {slide['page_num']}: {slide['title']}\n")
                for content_line in slide['content'][:3]:  # First 3 lines
                    f.write(f"  {content_line}\n")
                f.write("\n")
        
        f.write("\nKEY FINDINGS:\n")
        f.write("-" * 20 + "\n")
        for finding in analysis['key_findings'][:10]:  # Top 10 findings
            f.write(f"• {finding}\n")
        
        f.write("\nDATA INSIGHTS:\n")
        f.write("-" * 20 + "\n")
        for insight in analysis['data_insights'][:10]:  # Top 10 insights
            f.write(f"• {insight}\n")
    
    print("Content analysis saved to 'pdf_content_analysis.txt'")
    
    # Return content for further processing
    return content, analysis

if __name__ == "__main__":
    main()
