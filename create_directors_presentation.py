#!/usr/bin/env python3
"""
QS WUR Directors Presentation Creator
Author: Dr<PERSON>, Symbiosis International (Deemed University)
Purpose: Create professional PowerPoint presentation for directors meeting
Date: June 24, 2025
"""

from pptx import Presentation
from pptx.util import <PERSON><PERSON>, Pt
from pptx.enum.text import PP_ALIGN, MSO_ANCHOR
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import os

def setup_presentation_style():
    """Setup consistent styling for the presentation."""
    return {
        'primary_color': RGBColor(59, 130, 246),  # Blue
        'secondary_color': RGBColor(16, 185, 129),  # Green
        'accent_color': RGBColor(245, 101, 101),  # Red
        'text_color': RGBColor(31, 41, 55),  # Dark gray
        'light_gray': RGBColor(243, 244, 246),  # Light gray
        'title_font_size': Pt(32),
        'subtitle_font_size': Pt(24),
        'body_font_size': Pt(18),
        'small_font_size': Pt(14)
    }

def create_title_slide(prs, styles):
    """Create the title slide."""
    slide_layout = prs.slide_layouts[0]  # Title slide layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "QS World University Rankings 2026:\nStrategic Analysis for SIU"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Subtitle
    subtitle = slide.placeholders[1]
    subtitle.text = "Directors Meeting Presentation\nJune 26, 2025\n\nDr. Dharmendra Pandey\nDeputy Director, QMB | Head, QA\nSymbiosis International (Deemed University)"
    for paragraph in subtitle.text_frame.paragraphs:
        paragraph.font.size = styles['subtitle_font_size']
        paragraph.font.color.rgb = styles['text_color']
    
    return slide

def create_agenda_slide(prs, styles):
    """Create the agenda slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "Agenda"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Content
    content = slide.placeholders[1]
    agenda_items = [
        "1. Introduction & Context",
        "   • SIU's QS Rankings Journey",
        "   • Current Global Position",
        "",
        "2. Research Methodology",
        "   • Data Sources & Analysis Framework",
        "   • Evaluation Approach",
        "",
        "3. Key Findings & Performance Analysis",
        "   • Competitive Positioning",
        "   • Strengths & Opportunities",
        "",
        "4. Strategic Recommendations",
        "   • Priority Initiatives",
        "   • Implementation Roadmap",
        "",
        "5. Next Steps & Discussion"
    ]
    
    content.text = "\n".join(agenda_items)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if paragraph.text.startswith(('1.', '2.', '3.', '4.', '5.')):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']
    
    return slide

def create_introduction_slide(prs, styles):
    """Create the introduction slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "Introduction & Context"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Content
    content = slide.placeholders[1]
    intro_content = [
        "QS World University Rankings Overview",
        "• Global benchmark for university performance",
        "• Evaluates 1,500+ universities worldwide",
        "• Key metrics: Academic reputation, employer reputation, research impact",
        "",
        "SIU's Rankings Journey",
        "• 2025: Debut entry at rank 641 globally",
        "• 2026: Current position 696 globally",
        "• Achievement: 5th among 24 Indian private institutions",
        "",
        "Strategic Significance",
        "• Global visibility and recognition",
        "• Student recruitment and partnerships",
        "• Research collaboration opportunities",
        "• Institutional benchmarking and improvement"
    ]
    
    content.text = "\n".join(intro_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']
    
    return slide

def create_methodology_slide(prs, styles):
    """Create the research methodology slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "Research Methodology"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Content
    content = slide.placeholders[1]
    methodology_content = [
        "Data Sources",
        "• QS World University Rankings datasets (2022-2026)",
        "• Official QS methodology documentation",
        "• Institutional classification and profile data",
        "",
        "Analysis Framework",
        "• Comprehensive trend analysis across 5 years",
        "• Competitive benchmarking with peer institutions",
        "• Performance metric evaluation and gap analysis",
        "• Strategic positioning assessment",
        "",
        "Evaluation Approach",
        "• Quantitative analysis of ranking metrics",
        "• Qualitative assessment of institutional strengths",
        "• Comparative analysis with Indian private institutions",
        "• Strategic recommendations based on data insights"
    ]
    
    content.text = "\n".join(methodology_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']
    
    return slide

def create_conclusion_slide(prs, styles):
    """Create the conclusion slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "Key Findings & Conclusions"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Content
    content = slide.placeholders[1]
    conclusion_content = [
        "Performance Highlights",
        "• Global Rank: 696 (2026) | Strong debut performance",
        "• Employer Reputation: Rank 51 globally (Exceptional strength)",
        "• Competitive Position: 5th among 24 Indian private institutions",
        "",
        "Strategic Strengths",
        "• Outstanding industry connections and placement success",
        "• Comprehensive university status provides strategic flexibility",
        "• Very high research intensity classification",
        "• Strong foundation for sustainable growth",
        "",
        "Key Opportunities",
        "• Research output enhancement (primary focus area)",
        "• International collaboration expansion",
        "• Academic reputation building among global peers",
        "• Student diversity and internationalization"
    ]
    
    content.text = "\n".join(conclusion_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']
    
    return slide

def create_recommendations_slide(prs, styles):
    """Create the recommendations slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)
    
    # Title
    title = slide.shapes.title
    title.text = "Strategic Recommendations"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True
    
    # Content
    content = slide.placeholders[1]
    recommendations_content = [
        "High Priority Initiatives (1-2 Years)",
        "• Research Excellence Program: Target 50% citations improvement",
        "• International Collaboration: Develop 3-5 strategic partnerships",
        "• Faculty Development: Enhance research capabilities",
        "",
        "Medium Priority Initiatives (2-3 Years)",
        "• Comprehensive University Advantage: Leverage interdisciplinary opportunities",
        "• Industry Partnership Amplification: Build on employer reputation strength",
        "• Academic Reputation Enhancement: Global peer recognition",
        "",
        "Expected Outcomes",
        "• Target: Top 500 global ranking within 5 years",
        "• Enhanced research output and international visibility",
        "• Sustained employer reputation excellence",
        "• Established leadership in comprehensive education"
    ]
    
    content.text = "\n".join(recommendations_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']
    
    return slide

def create_performance_highlights_slide(prs, styles):
    """Create a detailed performance highlights slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)

    # Title
    title = slide.shapes.title
    title.text = "SIU Performance Highlights"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True

    # Content
    content = slide.placeholders[1]
    performance_content = [
        "Global Rankings Performance",
        "• 2025: Rank 641 (Debut entry)",
        "• 2026: Rank 696 (Current position)",
        "• Trajectory: Initial adjustment expected for new entrants",
        "",
        "Exceptional Strengths",
        "• Employer Reputation: Global Rank 51 (Score: 94.7)",
        "• Top 1% worldwide in industry connections",
        "• Outstanding placement success and industry partnerships",
        "",
        "Competitive Positioning",
        "• 5th among 24 Indian private institutions (Top 21%)",
        "• Comprehensive university status (Strategic advantage)",
        "• Very high research intensity classification",
        "",
        "Market Context",
        "• Indian private sector growth: 140% increase (2022-2026)",
        "• Strong positioning in expanding competitive landscape"
    ]

    content.text = "\n".join(performance_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']

    return slide

def create_next_steps_slide(prs, styles):
    """Create the next steps slide."""
    slide_layout = prs.slide_layouts[1]  # Title and content layout
    slide = prs.slides.add_slide(slide_layout)

    # Title
    title = slide.shapes.title
    title.text = "Next Steps & Implementation"
    title.text_frame.paragraphs[0].font.size = styles['title_font_size']
    title.text_frame.paragraphs[0].font.color.rgb = styles['primary_color']
    title.text_frame.paragraphs[0].font.bold = True

    # Content
    content = slide.placeholders[1]
    next_steps_content = [
        "Immediate Actions (Next 30 Days)",
        "• Approve research excellence initiative funding",
        "• Authorize international partnership development",
        "• Establish dedicated ranking improvement task force",
        "",
        "Short-term Implementation (3-6 Months)",
        "• Launch faculty development programs",
        "• Begin international collaboration discussions",
        "• Implement publication incentive systems",
        "",
        "Medium-term Goals (6-18 Months)",
        "• Secure 3-5 strategic international partnerships",
        "• Demonstrate 25% improvement in research metrics",
        "• Enhance international student recruitment",
        "",
        "Success Monitoring",
        "• Quarterly progress reviews with leadership",
        "• Annual comprehensive strategy assessment",
        "• Continuous benchmarking with peer institutions"
    ]

    content.text = "\n".join(next_steps_content)
    for paragraph in content.text_frame.paragraphs:
        paragraph.font.size = styles['body_font_size']
        paragraph.font.color.rgb = styles['text_color']
        if not paragraph.text.startswith('•'):
            paragraph.font.bold = True
            paragraph.font.color.rgb = styles['primary_color']

    return slide

def main():
    """Main function to create the presentation."""
    print("Creating QS WUR Directors Presentation...")

    # Create presentation
    prs = Presentation()
    styles = setup_presentation_style()

    # Create slides
    print("Creating title slide...")
    create_title_slide(prs, styles)

    print("Creating agenda slide...")
    create_agenda_slide(prs, styles)

    print("Creating introduction slide...")
    create_introduction_slide(prs, styles)

    print("Creating methodology slide...")
    create_methodology_slide(prs, styles)

    print("Creating performance highlights slide...")
    create_performance_highlights_slide(prs, styles)

    print("Creating conclusion slide...")
    create_conclusion_slide(prs, styles)

    print("Creating recommendations slide...")
    create_recommendations_slide(prs, styles)

    print("Creating next steps slide...")
    create_next_steps_slide(prs, styles)

    # Save presentation
    output_file = "QS_WUR_Directors_Presentation_June_2025.pptx"
    prs.save(output_file)

    print(f"Presentation saved as: {output_file}")
    print("Presentation created successfully!")

    return output_file

if __name__ == "__main__":
    main()
