# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive QS World University Rankings analysis project containing ranking data from 2022-2026, specifically focused on Symbiosis International (Deemed University) performance analysis. The repository includes a complete analysis framework with data processing, visualization, and professional document generation capabilities for institutional assessment and quality benchmarking in higher education.

## Project Status: COMPLETE ANALYSIS SYSTEM

**✅ Implemented Components:**
- Complete data loading and preprocessing pipeline
- Symbiosis performance deep-dive analysis
- Indian private institutions comparative analysis
- Institutional focus classification change impact analysis
- Professional visualization suite with Symbiosis branding
- MS Word and PowerPoint document generation system
- Executive summary and strategic recommendations framework

## Data Structure

The project contains QS World University Rankings datasets with the following key metrics:
- Academic Reputation (Score and Rank)
- Employer Reputation (Score and Rank)
- Faculty/Student Ratio (Score and Rank)
- Citations per Faculty (Score and Rank)
- International Faculty Ratio (Score and Rank)
- International Students Ratio (Score and Rank)
- International Research Network (Score and Rank)
- Employment Outcomes (Score and Rank)
- Sustainability (Score and Rank)

Each dataset includes institutional metadata:
- Institution Name and Country
- Private/Government classification
- Size (S/M/L/XL)
- Focus (FC=Full Comprehensive, CO=Comprehensive, FO=Focused, SP=Specialist)
- Research intensity (VH=Very High, H=High, M=Medium, L=Limited)

## Development Approach & Implemented Architecture

### **Core Analysis Pipeline (IMPLEMENTED)**

The project follows a modular architecture with the following key components:

1. **Data Loading & Preprocessing** (`src/data/data_loader.py`)
   - Handles multiple CSV encodings and data standardization
   - Processes all years (2022-2026) with consistent column mapping
   - Filters for Indian institutions and private institutions specifically
   - Exports cleaned datasets for analysis

2. **Analysis Modules**:
   - **Symbiosis Analysis** (`src/analysis/symbiosis_analysis.py`): Performance deep-dive, competitive positioning
   - **Comparative Analysis** (`src/analysis/comparative_analysis.py`): Indian private institutions landscape
   - **Focus Change Analysis** (`src/analysis/focus_change_analysis.py`): FO→CO transition impact

3. **Visualization Suite** (`src/visualization/`):
   - Professional charts with Symbiosis branding (`styling.py`)
   - Comprehensive chart generation (`charts.py`)
   - Executive dashboard and strategic visualizations

4. **Document Generation** (`src/report_generator/`):
   - MS Word report generator with professional formatting
   - PowerPoint presentation generator for directors meetings
   - Executive summaries and strategic action plans

### **Key Commands & Usage**

**🔧 Main Analysis Execution:**
```bash
# Activate virtual environment and run main analysis
source document_env/bin/activate
python main_analysis.py
```

**📄 Generate Professional Documents:**
```bash
# Generate Word, PowerPoint, and text reports
source document_env/bin/activate
python generate_simple_documents.py
```

**📊 Outputs Generated:**
- `output/`: Processed datasets and analysis results
- `output/figures/`: High-resolution charts and visualizations
- `output/reports/`: JSON analysis reports
- `final_documents/`: Professional Word/PowerPoint deliverables

## Key Analytical Considerations

- Rankings data requires careful handling of ties and missing institutions across years
- Score normalization may be needed for cross-year comparisons
- International metrics may have different calculation methodologies across years
- Focus on institutional improvement trajectories rather than absolute rankings

## Analysis Results & Key Findings

### **Symbiosis International Performance Summary:**
- **Global Ranking:** 696 (2026), entered rankings in 2025 at 641
- **Employer Reputation:** Exceptional - Global Rank 51 (Score: 94.7)
- **Competitive Position:** 5th among 24 Indian private institutions
- **Focus Classification:** Successfully transitioned from "Focused" (FO) to "Comprehensive" (CO)
- **Key Strength:** Top 1% globally for employer reputation
- **Primary Improvement Area:** Research citations (Rank 801)

### **Strategic Recommendations Implemented:**
1. **Research Excellence Initiative** (High Priority - 1-2 years)
2. **International Collaboration Expansion** (High Priority - 1-3 years)
3. **Comprehensive University Advantage** (Medium Priority - 2-3 years)
4. **Industry Partnership Amplification** (Medium Priority - 1-2 years)

## Professional Deliverables Generated

### **📊 Directors Meeting Package:**
- **MS Word Report:** Comprehensive 25+ page analysis with tables, charts, strategic recommendations
- **PowerPoint Presentation:** Executive-level slides for 45-60 minute presentation
- **Executive Summary:** 2-page concise overview for board distribution
- **Strategic Action Plan:** Detailed implementation roadmap with timelines
- **Comprehensive Text Report:** Complete analysis in text format for reference

### **🎯 Document Quality Standards:**
- Professional Symbiosis branding and color schemes
- High-resolution charts (300 DPI) ready for printing
- Executive-appropriate content depth and formatting
- Compatible with Microsoft Office 2016+
- Optimized for both digital presentation and print distribution

## Dependencies & Environment

### **Required Python Packages:**
```bash
pip install python-docx python-pptx matplotlib seaborn pandas numpy
```

### **Virtual Environment Setup:**
```bash
python3 -m venv document_env
source document_env/bin/activate
pip install -r requirements.txt  # if available
```

## Technical Implementation Notes

### **Data Handling:**
- Multiple CSV encoding support (UTF-8, Latin-1, ISO-8859-1, CP1252)
- Robust handling of "Nan" values and missing data points
- Consistent column standardization across years
- Institution name normalization for accurate matching

### **Analysis Architecture:**
- Modular design for easy extension and maintenance
- Factory pattern for visualization generation
- Template-based document generation
- Comprehensive error handling and logging

### **File Structure:**
```
├── src/
│   ├── data/           # Data loading and preprocessing
│   ├── analysis/       # Analysis modules
│   ├── visualization/  # Chart generation and styling
│   └── report_generator/ # Document generation
├── output/             # Processed data and analysis results
├── final_documents/    # Professional deliverables
├── document_env/       # Python virtual environment
└── *.csv              # Raw QS WUR datasets (2022-2026)
```

## Future Development Guidelines

When extending this project:

1. **New Analysis Modules:** Follow the pattern in `src/analysis/` with comprehensive docstrings
2. **Additional Visualizations:** Extend `src/visualization/charts.py` with consistent branding
3. **Document Templates:** Modify `src/report_generator/` for new output formats
4. **Data Updates:** Use `src/data/data_loader.py` as the central data processing pipeline

## Contact & Support

**Analysis Framework Created by:**
Dr. Dharmendra Pandey  
Deputy Director - Quality Management & Benchmarking (QMB)  
Head - Quality Assurance (QA)  
Symbiosis International (Deemed University)  
Email: <EMAIL> | <EMAIL>

**System Status:** Production-ready comprehensive analysis framework  
**Last Updated:** June 2025  
**Analysis Period:** QS WUR 2022-2026