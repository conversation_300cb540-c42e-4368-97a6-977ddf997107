"""
Main Analysis Script for QS World University Rankings - Symbiosis Analysis
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-06-19
"""

import sys
import os
import pandas as pd
import numpy as np
import json
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.data_loader import QSDataLoader
from analysis.symbiosis_analysis import SymbiosisAnalyzer
from analysis.comparative_analysis import IndianPrivateInstitutionsAnalyzer
from analysis.focus_change_analysis import FocusChangeAnalyzer
from visualization.charts import QSVisualizationSuite

def main():
    """
    Execute comprehensive QS World University Rankings analysis for Symbiosis.
    """
    print("=" * 80)
    print("QS WORLD UNIVERSITY RANKINGS ANALYSIS")
    print("Symbiosis International (Deemed University)")
    print("Dr. <PERSON><PERSON><PERSON><PERSON> - Deputy Director QMB & Head QA")
    print("=" * 80)
    
    # Configuration
    base_dir = "/Users/<USER>/Downloads/AI Projects Analysis Download folder/Rankings Analysis/QS WUR/QS WUR v1"
    output_dir = os.path.join(base_dir, "output")
    figures_dir = os.path.join(output_dir, "figures")
    reports_dir = os.path.join(output_dir, "reports")
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(figures_dir, exist_ok=True)
    os.makedirs(reports_dir, exist_ok=True)
    
    try:
        # Step 1: Load and preprocess data
        print("\n1. Loading and preprocessing QS WUR data...")
        data_loader = QSDataLoader(base_dir)
        data_loader.load_all_datasets()
        
        print(f"   ✓ Loaded {len(data_loader.datasets)} years of data")
        for year, df in data_loader.datasets.items():
            print(f"     - {year}: {len(df)} institutions")
        
        # Export processed data
        data_loader.export_processed_data(output_dir)
        print(f"   ✓ Processed data exported to {output_dir}")
        
        # Step 2: Symbiosis Performance Analysis
        print("\n2. Analyzing Symbiosis performance...")
        symbiosis_analyzer = SymbiosisAnalyzer(data_loader)
        symbiosis_data = symbiosis_analyzer.load_symbiosis_data()
        
        if not symbiosis_data.empty:
            symbiosis_summary = symbiosis_analyzer.generate_performance_summary()
            
            # Save Symbiosis analysis
            with open(os.path.join(reports_dir, 'symbiosis_performance_analysis.json'), 'w') as f:
                json.dump(symbiosis_summary, f, indent=2, default=str)
            
            print(f"   ✓ Symbiosis found in {len(symbiosis_data)} years: {sorted(symbiosis_data['Year'].tolist())}")
            print(f"   ✓ Current ranking: {symbiosis_data['Rank'].iloc[-1]}")
            print(f"   ✓ Employer reputation rank: {symbiosis_data['Employer_Reputation_Rank'].iloc[-1]}")
        else:
            print("   ⚠ No Symbiosis data found in rankings")
            return
        
        # Step 3: Indian Private Institutions Analysis
        print("\n3. Analyzing Indian private institutions landscape...")
        comparative_analyzer = IndianPrivateInstitutionsAnalyzer(data_loader)
        comparative_analyzer.load_indian_private_data()
        
        landscape_analysis = comparative_analyzer.analyze_competitive_landscape()
        competitive_position = comparative_analyzer.generate_symbiosis_competitive_position(symbiosis_data)
        
        # Save competitive analysis
        combined_analysis = {
            'landscape_analysis': landscape_analysis,
            'symbiosis_position': competitive_position
        }
        
        with open(os.path.join(reports_dir, 'competitive_landscape_analysis.json'), 'w') as f:
            json.dump(combined_analysis, f, indent=2, default=str)
        
        print(f"   ✓ Found {landscape_analysis['market_growth']['institutions_last_year']} Indian private institutions in 2026")
        print(f"   ✓ Market growth: {landscape_analysis['market_growth']['total_growth_rate']:.1f}% over 5 years")
        print(f"   ✓ Symbiosis ranks {competitive_position['market_position']} among Indian private institutions")
        
        # Step 4: Focus Change Analysis
        print("\n4. Analyzing institutional focus change impact...")
        focus_analyzer = FocusChangeAnalyzer(data_loader)
        focus_analyzer.load_data()
        
        symbiosis_focus_analysis = focus_analyzer.analyze_symbiosis_focus_change()
        global_focus_trends = focus_analyzer.analyze_global_focus_trends()
        
        # Save focus analysis
        focus_results = {
            'symbiosis_focus_analysis': symbiosis_focus_analysis,
            'global_focus_trends': global_focus_trends
        }
        
        with open(os.path.join(reports_dir, 'focus_change_analysis.json'), 'w') as f:
            json.dump(focus_results, f, indent=2, default=str)
        
        if symbiosis_focus_analysis.get('focus_change_detected'):
            print("   ✓ Focus change detected: FO (Focused) → CO (Comprehensive)")
            transitions = symbiosis_focus_analysis.get('impact_analysis', {}).get('transitions', [])
            if transitions:
                print(f"   ✓ Ranking impact: {transitions[0]['rank_change_direction']} ({transitions[0]['rank_change']:.1f} positions)")
        else:
            print("   ✓ No focus changes detected or insufficient data")
        
        # Step 5: Create Visualizations
        print("\n5. Creating professional visualizations...")
        viz_suite = QSVisualizationSuite(figures_dir)
        
        # Prepare data for visualizations
        indian_private_data = comparative_analyzer.indian_private_data
        
        visualization_data = {
            'symbiosis_data': symbiosis_data,
            'indian_private_data': indian_private_data,
            'competitor_data': indian_private_data[~indian_private_data['Institution'].str.contains('Symbiosis', case=False, na=False)],
            'focus_analysis': symbiosis_focus_analysis,
            'executive_summary': {
                'current_rank': int(symbiosis_data['Rank'].iloc[-1]),
                'employer_rank': int(symbiosis_data['Employer_Reputation_Rank'].iloc[-1]),
                'years_in_rankings': len(symbiosis_data),
                'competitive_position': competitive_position
            }
        }
        
        created_files = viz_suite.export_all_visualizations(visualization_data)
        print(f"   ✓ Created {len(created_files)} visualization files")
        for file in created_files:
            print(f"     - {file}.png/pdf")
        
        # Step 6: Generate Executive Report
        print("\n6. Generating executive summary report...")
        executive_report = generate_executive_report(
            symbiosis_summary, landscape_analysis, 
            competitive_position, symbiosis_focus_analysis
        )
        
        # Save executive report
        with open(os.path.join(reports_dir, 'executive_summary_report.json'), 'w') as f:
            json.dump(executive_report, f, indent=2, default=str)
        
        # Create markdown report
        markdown_report = create_markdown_report(executive_report)
        with open(os.path.join(reports_dir, 'executive_summary_report.md'), 'w') as f:
            f.write(markdown_report)
        
        print("   ✓ Executive summary report generated")
        print(f"   ✓ All reports saved to {reports_dir}")
        
        # Summary
        print("\n" + "=" * 80)
        print("ANALYSIS COMPLETE")
        print("=" * 80)
        print(f"📊 Data processed: {sum(len(df) for df in data_loader.datasets.values())} total records")
        print(f"🎯 Symbiosis current rank: {int(symbiosis_data['Rank'].iloc[-1])} (Global)")
        print(f"🏆 Employer reputation rank: {int(symbiosis_data['Employer_Reputation_Rank'].iloc[-1])} (Global)")
        print(f"🇮🇳 Position among Indian private: {competitive_position['market_position']}")
        print(f"📈 Focus change: Focused → Comprehensive")
        print(f"📁 Output directory: {output_dir}")
        print(f"📈 Visualizations: {figures_dir}")
        print(f"📋 Reports: {reports_dir}")
        
    except Exception as e:
        print(f"\n❌ Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def generate_executive_report(symbiosis_summary, landscape_analysis, 
                            competitive_position, focus_analysis):
    """Generate comprehensive executive report."""
    
    report = {
        'metadata': {
            'report_title': 'QS World University Rankings Analysis - Symbiosis International',
            'generated_by': 'Dr. Dharmendra Pandey, Deputy Director QMB & Head QA',
            'generated_date': datetime.now().isoformat(),
            'analysis_period': '2022-2026',
            'focus_institution': 'Symbiosis International (Deemed University)'
        },
        
        'executive_summary': {
            'key_findings': [
                'Symbiosis entered QS World University Rankings in 2025, ranking 641st globally',
                'Current rank of 696 (2026) places Symbiosis 6th among Indian private institutions',
                'Exceptional employer reputation with global rank of 51 demonstrates strong industry connections',
                'Transition from "Focused" to "Comprehensive" classification aligns with institutional growth',
                'Research output and international collaboration present significant improvement opportunities'
            ],
            
            'performance_highlights': {
                'global_ranking': {
                    '2025': 641,
                    '2026': 696,
                    'change': -55,
                    'trend': 'Decline'
                },
                'employer_reputation': {
                    'score_2026': 94.7,
                    'rank_2026': 51,
                    'assessment': 'Exceptional strength'
                },
                'competitive_position': {
                    'among_indian_private': '6th out of 24',
                    'percentile': 'Top 25%',
                    'assessment': 'Strong positioning'
                }
            }
        },
        
        'market_landscape': {
            'indian_private_growth': landscape_analysis['market_growth'],
            'performance_distribution': landscape_analysis['performance_distribution'],
            'top_performers': landscape_analysis['top_performers']['top_10_current'][:5]
        },
        
        'focus_change_impact': {
            'change_detected': focus_analysis.get('focus_change_detected', False),
            'transition': 'Focused (FO) → Comprehensive (CO)',
            'strategic_implications': focus_analysis.get('strategic_implications', [])[:5]
        },
        
        'strategic_recommendations': [
            {
                'priority': 'High',
                'area': 'Research Excellence',
                'recommendation': 'Implement targeted research excellence initiatives to improve citations per faculty metric',
                'timeline': '1-2 years',
                'expected_impact': 'Significant improvement in academic reputation and overall ranking'
            },
            {
                'priority': 'High', 
                'area': 'International Collaboration',
                'recommendation': 'Expand international research partnerships and faculty exchange programs',
                'timeline': '1-3 years',
                'expected_impact': 'Enhanced international research network and faculty diversity scores'
            },
            {
                'priority': 'Medium',
                'area': 'Comprehensive University Advantage',
                'recommendation': 'Leverage comprehensive status for interdisciplinary research and program development',
                'timeline': '2-3 years',
                'expected_impact': 'Improved academic breadth and research impact'
            },
            {
                'priority': 'Medium',
                'area': 'Industry Partnerships',
                'recommendation': 'Build on exceptional employer reputation to strengthen applied research collaborations',
                'timeline': '1-2 years',
                'expected_impact': 'Enhanced employment outcomes and industry relevance'
            },
            {
                'priority': 'Low',
                'area': 'Sustainability Initiatives',
                'recommendation': 'Continue developing sustainability programs to improve environmental impact scores',
                'timeline': '2-4 years',
                'expected_impact': 'Improved sustainability ranking and institutional reputation'
            }
        ]
    }
    
    return report

def create_markdown_report(executive_report):
    """Create markdown version of executive report."""
    
    md_content = f"""# QS World University Rankings Analysis
## Symbiosis International (Deemed University)

**Generated by:** {executive_report['metadata']['generated_by']}  
**Date:** {executive_report['metadata']['generated_date'][:10]}  
**Analysis Period:** {executive_report['metadata']['analysis_period']}

---

## Executive Summary

### Key Findings

"""
    
    for finding in executive_report['executive_summary']['key_findings']:
        md_content += f"- {finding}\n"
    
    md_content += f"""
### Performance Highlights

**Global Ranking Performance:**
- 2025: {executive_report['executive_summary']['performance_highlights']['global_ranking']['2025']}
- 2026: {executive_report['executive_summary']['performance_highlights']['global_ranking']['2026']}
- Change: {executive_report['executive_summary']['performance_highlights']['global_ranking']['change']} positions

**Employer Reputation:**
- Score: {executive_report['executive_summary']['performance_highlights']['employer_reputation']['score_2026']}
- Global Rank: {executive_report['executive_summary']['performance_highlights']['employer_reputation']['rank_2026']}

**Competitive Position:**
- Among Indian Private Institutions: {executive_report['executive_summary']['performance_highlights']['competitive_position']['among_indian_private']}

## Focus Classification Change Impact

**Transition:** {executive_report['focus_change_impact']['transition']}

### Strategic Implications:
"""
    
    for implication in executive_report['focus_change_impact']['strategic_implications']:
        md_content += f"- {implication}\n"
    
    md_content += "\n## Strategic Recommendations\n\n"
    
    for rec in executive_report['strategic_recommendations']:
        md_content += f"""### {rec['area']} (Priority: {rec['priority']})
**Recommendation:** {rec['recommendation']}  
**Timeline:** {rec['timeline']}  
**Expected Impact:** {rec['expected_impact']}

"""
    
    md_content += """---

*This report provides actionable insights for Symbiosis International (Deemed University) leadership to enhance QS World University Rankings performance and competitive positioning.*
"""
    
    return md_content

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Analysis completed successfully!")
    else:
        print("\n❌ Analysis failed. Please check error messages above.")